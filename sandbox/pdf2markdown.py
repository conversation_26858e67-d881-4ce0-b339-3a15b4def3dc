# pip install pymupdf pymupdf4llm
# pip install ocrmypdf pymupdf pymupdf4llm
import asyncio
import os
import subprocess, tempfile, pathlib, pymupdf4llm
from dotenv import load_dotenv

load_dotenv()
SRC  = pathlib.Path("data/projects/demo/documents/13F-SFA_genotox_1.pdf")
DEST = SRC.with_suffix(".md")

test_temp_dir = "tmp"
if not os.path.exists(test_temp_dir):
    os.makedirs(test_temp_dir)
# ocrd = pathlib.Path(test_temp_dir) / "ocr.pdf"
# subprocess.run(
#         [
#             "ocrmypdf",
#             "--force-ocr",            # ← key change!
#             "--deskew",
#             "--clean",
#             "-l", "eng",              # language
#             SRC, ocrd
#         ],
#         check=True,
#     )

# md_text = pymupdf4llm.to_markdown(ocrd)  # text layer now present
# print(md_text[:800], "…\n")                             # preview

# DEST.write_text(md_text, encoding="utf-8")
# print(f"Wrote {DEST} ({DEST.stat().st_size} bytes)")


## METHOD 2

from llama_cloud_services import LlamaParse

parser = LlamaParse(
    api_key=os.getenv("LLAMA_CLOUD_API_KEY"),  # can also be set in your env as LLAMA_CLOUD_API_KEY
    num_workers=4,       # if multiple files passed, split in `num_workers` API calls
    verbose=True,
    language="en",       # optionally define a language, default=en
)

async def main():
    # async
    result = await parser.aparse(str(SRC))
    print(result)
    

    # get the llama-index markdown documents
    markdown_documents = result.get_markdown_documents(split_by_page=True)
    print(markdown_documents)
    
    markdown_content = "\n".join([f"Page_{ix}:\n{doc.text}" for ix, doc in enumerate(markdown_documents)])
    DEST.write_text(markdown_content, encoding="utf-8")
    print(f"Wrote {DEST} ({DEST.stat().st_size} bytes)")
    # access the raw job result
    # Items will vary based on the parser configuration
    for page in result.pages:
        print(page.text)
        print(page.md)
        print(page.images)
        print(page.layout)
        print(page.structuredData)

    
if __name__ == "__main__":
    asyncio.run(main())
    