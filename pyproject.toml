[project]
name = "aria"
version = "0.1.0"
description = "ARIA - Automated Regulatory Intelligence Assistant, is an advanced document analysis system designed to automate complex regulatory and scientific data extraction from study reports and technical documentation. ARIA solves the critical problem of manually reviewing hundreds of pages of dense scientific documentation to verify compliance with regulatory requirements or extract key scientific findings."
authors = [
    {name = "<PERSON><PERSON>",email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.10,<4.0"
dependencies = [
    "openai (>=1.78.1,<2.0.0)",
    "baml-py (==0.88.0)",
    "langgraph (>=0.4.5,<0.5.0)",
    "pdfplumber (>=0.11.6,<0.12.0)",
    "python-dotenv (>=1.1.0,<2.0.0)",
    "opentelemetry-api>=1.24.0,<2.0.0",
    "opentelemetry-sdk>=1.24.0,<2.0.0",
    "graphiti-core (>=0.11.6,<0.12.0)",
    "langchain-openai (>=0.3.17,<0.4.0)",
    "typer[all] (>=0.15.4,<0.16.0)",
    "rich (>=14.0.0,<15.0.0)",
    "pydantic (>=2.11.5,<3.0.0)",
    "tomli (>=2.2.1,<3.0.0)",
    "pydantic-settings (>=2.9.1,<3.0.0)",
    "pymupdf (>=1.26.0,<2.0.0)",
    "pymupdf4llm (>=0.0.24,<0.0.25)",
    "click (>=8.0,<9.0)",
    "ocrmypdf (>=16.10.1,<17.0.0)",
    "pytesseract (>=0.3.13,<0.4.0)",
    "llama-cloud-services (>=0.6.23,<0.7.0)",
]

packages = [
  { include = "aria", from = "." }
]

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
name = "aria"
version = "0.1.0"

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.5"

[tool.poetry.scripts]
aria = "aria_cli.__main__:app"