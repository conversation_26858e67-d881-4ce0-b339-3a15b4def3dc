#!/usr/bin/env python3
"""
Graphiti Database Management Script

Provides comprehensive database management capabilities for Graphiti knowledge graphs:
- Delete entire database content
- Get all group IDs (namespaces)
- Delete specific group IDs
- Get summary statistics for database or specific groups
- Export/backup group data
"""

import asyncio
import json
import os
import sys
from datetime import datetime
from typing import List, Dict, Any, Optional, Set
from pathlib import Path

from graphiti_core import Graphiti
from graphiti_core.search.search_config_recipes import (
    NODE_HYBRID_SEARCH_RRF,
    EDGE_HYBRID_SEARCH_RRF,
    COMBINED_HYBRID_SEARCH_RRF
)
from aria.memory.graphiti_base import _GraphitiBase

class GraphitiAdminManager(_GraphitiBase):
    """Manages Graphiti database operations and maintenance"""
    
    def __init__(self, graphiti: Graphiti):
        super().__init__(graphiti)
    
    async def delete_entire_database(self, confirm: bool = False) -> Dict[str, Any]:
        """
        Delete all content from the Graphiti database
        
        Args:
            confirm: Safety confirmation flag
            
        Returns:
            Dictionary with deletion results
        """
        if not confirm:
            print("⚠️  WARNING: This will delete ALL data in the database!")
            print("   Use --confirm flag to proceed with deletion")
            return {"deleted": False, "reason": "confirmation_required"}
        
        print("🗑️  Deleting entire database content...")
        
        try:
            # Get initial counts
            initial_stats = await self._get_raw_database_stats()
            
            # Delete all relationships first (Neo4j requirement)
            print("   Deleting all relationships...")
            result = await self.driver.execute_query(
                "MATCH ()-[r]-() DELETE r RETURN count(r) as deleted_relationships"
            )
            deleted_relationships = result.records[0]["deleted_relationships"]
            
            # Delete all nodes
            print("   Deleting all nodes...")
            result = await self.driver.execute_query(
                "MATCH (n) DELETE n RETURN count(n) as deleted_nodes"
            )
            deleted_nodes = result.records[0]["deleted_nodes"]
            
            print(f"✅ Database cleared successfully!")
            print(f"   Deleted {deleted_nodes:,} nodes")
            print(f"   Deleted {deleted_relationships:,} relationships")
            
            return {
                "deleted": True,
                "deleted_nodes": deleted_nodes,
                "deleted_relationships": deleted_relationships,
                "initial_stats": initial_stats,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            print(f"❌ Error deleting database: {e}")
            return {"deleted": False, "error": str(e)}
    
    async def get_all_group_ids(self) -> List[str]:
        """
        Get all unique group IDs (namespaces) in the database
        
        Returns:
            List of group ID strings
        """
        print("🔍 Scanning for all group IDs...")
        
        try:
            # Query all nodes and edges for group_id property
            result = await self.driver.execute_query("""
                MATCH (n)
                WHERE n.group_id IS NOT NULL AND n.group_id <> ""
                RETURN DISTINCT n.group_id as group_id
                UNION
                MATCH ()-[r]->()
                WHERE r.group_id IS NOT NULL AND r.group_id <> ""
                RETURN DISTINCT r.group_id as group_id
                ORDER BY group_id
            """)
            
            group_ids = [record["group_id"] for record in result.records]
            
            print(f"📊 Found {len(group_ids)} unique group IDs:")
            for i, group_id in enumerate(group_ids, 1):
                print(f"   {i:2d}. {group_id}")
            
            return group_ids
            
        except Exception as e:
            print(f"❌ Error getting group IDs: {e}")
            return []
    
    async def delete_group_ids(self, group_ids: List[str], confirm: bool = False) -> Dict[str, Any]:
        """
        Delete all data associated with specific group IDs
        
        Args:
            group_ids: List of group IDs to delete
            confirm: Safety confirmation flag
            
        Returns:
            Dictionary with deletion results
        """
        if not confirm:
            print(f"⚠️  WARNING: This will delete all data for {len(group_ids)} group ID(s):")
            for group_id in group_ids:
                print(f"     - {group_id}")
            print("   Use --confirm flag to proceed with deletion")
            return {"deleted": False, "reason": "confirmation_required"}
        
        print(f"🗑️  Deleting data for {len(group_ids)} group ID(s)...")
        
        deletion_results = {}
        total_deleted_nodes = 0
        total_deleted_relationships = 0
        
        try:
            for group_id in group_ids:
                print(f"   Processing group: {group_id}")
                
                # Get stats before deletion
                group_stats = await self.get_group_statistics(group_id, verbose=False)
                
                # Delete relationships in this group first
                rel_result = await self.driver.execute_query("""
                    MATCH ()-[r]->()
                    WHERE r.group_id = $group_id
                    DELETE r
                    RETURN count(r) as deleted_relationships
                """, group_id=group_id)
                
                deleted_rels = rel_result.records[0]["deleted_relationships"]
                
                # Delete nodes in this group
                node_result = await self.driver.execute_query("""
                    MATCH (n)
                    WHERE n.group_id = $group_id
                    DELETE n
                    RETURN count(n) as deleted_nodes
                """, group_id=group_id)
                
                deleted_nodes = node_result.records[0]["deleted_nodes"]
                
                deletion_results[group_id] = {
                    "deleted_nodes": deleted_nodes,
                    "deleted_relationships": deleted_rels,
                    "initial_stats": group_stats
                }
                
                total_deleted_nodes += deleted_nodes
                total_deleted_relationships += deleted_rels
                
                print(f"     ✅ Deleted {deleted_nodes} nodes, {deleted_rels} relationships")
            
            print(f"✅ Group deletion completed!")
            print(f"   Total deleted: {total_deleted_nodes:,} nodes, {total_deleted_relationships:,} relationships")
            
            return {
                "deleted": True,
                "group_results": deletion_results,
                "total_deleted_nodes": total_deleted_nodes,
                "total_deleted_relationships": total_deleted_relationships,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            print(f"❌ Error deleting groups: {e}")
            return {"deleted": False, "error": str(e), "partial_results": deletion_results}
    
    async def get_database_statistics(self, verbose: bool = True) -> Dict[str, Any]:
        """
        Get comprehensive statistics for the entire database
        
        Args:
            verbose: Whether to print detailed output
            
        Returns:
            Dictionary with database statistics
        """
        if verbose:
            print("📊 Gathering database statistics...")
        
        try:
            # Get raw counts
            raw_stats = await self._get_raw_database_stats()
            
            # Get group-specific information
            group_ids = await self.get_all_group_ids()
            
            # Get label distribution
            label_stats = await self._get_label_distribution()
            
            # Get relationship type distribution
            rel_type_stats = await self._get_relationship_type_distribution()
            
            stats = {
                "timestamp": datetime.now().isoformat(),
                "total_nodes": raw_stats["total_nodes"],
                "total_relationships": raw_stats["total_relationships"],
                "total_groups": len(group_ids),
                "group_ids": group_ids,
                "node_labels": label_stats,
                "relationship_types": rel_type_stats,
                "database_size_mb": await self._estimate_database_size()
            }
            
            if verbose:
                self._print_database_stats(stats)
            
            return stats
            
        except Exception as e:
            if verbose:
                print(f"❌ Error getting database statistics: {e}")
            return {"error": str(e)}
    
    async def get_group_statistics(self, group_id: str, verbose: bool = True) -> Dict[str, Any]:
        """
        Get detailed statistics for a specific group ID
        
        Args:
            group_id: Group ID to analyze
            verbose: Whether to print detailed output
            
        Returns:
            Dictionary with group statistics
        """
        if verbose:
            print(f"📊 Analyzing group: {group_id}")
        
        try:
            # Get node counts by label for this group
            node_result = await self.driver.execute_query("""
                MATCH (n)
                WHERE n.group_id = $group_id
                RETURN labels(n) as labels, count(n) as count
                ORDER BY count DESC
            """, group_id=group_id)
            
            node_stats = {}
            total_nodes = 0
            for record in node_result.records:
                labels = record["labels"]
                count = record["count"]
                label_key = ":".join(sorted(labels)) if labels else "no_label"
                node_stats[label_key] = count
                total_nodes += count
            
            # Get relationship counts by type for this group
            rel_result = await self.driver.execute_query("""
                MATCH ()-[r]->()
                WHERE r.group_id = $group_id
                RETURN type(r) as rel_type, count(r) as count
                ORDER BY count DESC
            """, group_id=group_id)
            
            rel_stats = {}
            total_relationships = 0
            for record in rel_result.records:
                rel_type = record["rel_type"]
                count = record["count"]
                rel_stats[rel_type] = count
                total_relationships += count
            
            # Get creation time range
            time_range = await self._get_group_time_range(group_id)
            
            # Try to find document information
            doc_info = await self._get_group_document_info(group_id)
            
            stats = {
                "group_id": group_id,
                "timestamp": datetime.now().isoformat(),
                "total_nodes": total_nodes,
                "total_relationships": total_relationships,
                "node_labels": node_stats,
                "relationship_types": rel_stats,
                "time_range": time_range,
                "document_info": doc_info
            }
            
            if verbose:
                self._print_group_stats(stats)
            
            return stats
            
        except Exception as e:
            if verbose:
                print(f"❌ Error getting group statistics: {e}")
            return {"group_id": group_id, "error": str(e)}
    
    async def export_group_data(self, group_id: str, output_file: str = None) -> Dict[str, Any]:
        """
        Export all data for a group ID to JSON file
        
        Args:
            group_id: Group ID to export
            output_file: Output file path (optional)
            
        Returns:
            Dictionary with export results
        """
        if output_file is None:
            output_file = f"graphiti_export_{group_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        print(f"📤 Exporting group {group_id} to {output_file}...")
        
        try:
            # Get all nodes in the group
            nodes_result = await self.driver.execute_query("""
                MATCH (n)
                WHERE n.group_id = $group_id
                RETURN n
            """, group_id=group_id)
            
            # Get all relationships in the group
            rels_result = await self.driver.execute_query("""
                MATCH (a)-[r]->(b)
                WHERE r.group_id = $group_id
                RETURN a, r, b
            """, group_id=group_id)
            
            export_data = {
                "export_info": {
                    "group_id": group_id,
                    "export_timestamp": datetime.now().isoformat(),
                    "node_count": len(nodes_result.records),
                    "relationship_count": len(rels_result.records)
                },
                "nodes": [dict(record["n"]) for record in nodes_result.records],
                "relationships": [
                    {
                        "source": dict(record["a"]),
                        "relationship": dict(record["r"]),
                        "target": dict(record["b"])
                    }
                    for record in rels_result.records
                ]
            }
            
            # Write to file
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, default=str)
            
            print(f"✅ Export completed: {output_file}")
            print(f"   Exported {len(export_data['nodes'])} nodes and {len(export_data['relationships'])} relationships")
            
            return {
                "exported": True,
                "output_file": output_file,
                "group_id": group_id,
                "node_count": len(export_data['nodes']),
                "relationship_count": len(export_data['relationships'])
            }
            
        except Exception as e:
            print(f"❌ Error exporting group: {e}")
            return {"exported": False, "error": str(e)}
    
    # Helper methods
    async def _get_raw_database_stats(self) -> Dict[str, int]:
        """Get basic node and relationship counts"""
        # Count all nodes
        node_result = await self.driver.execute_query("MATCH (n) RETURN count(n) as total_nodes")
        total_nodes = node_result.records[0]["total_nodes"]
        
        # Count all relationships
        rel_result = await self.driver.execute_query("MATCH ()-[r]->() RETURN count(r) as total_relationships")
        total_relationships = rel_result.records[0]["total_relationships"]
        
        return {
            "total_nodes": total_nodes,
            "total_relationships": total_relationships
        }
    
    async def _get_label_distribution(self) -> Dict[str, int]:
        """Get distribution of node labels"""
        result = await self.driver.execute_query("""
            MATCH (n)
            RETURN labels(n) as labels, count(n) as count
            ORDER BY count DESC
        """)
        
        label_stats = {}
        for record in result.records:
            labels = record["labels"]
            count = record["count"]
            label_key = ":".join(sorted(labels)) if labels else "no_label"
            label_stats[label_key] = count
        
        return label_stats
    
    async def _get_relationship_type_distribution(self) -> Dict[str, int]:
        """Get distribution of relationship types"""
        result = await self.driver.execute_query("""
            MATCH ()-[r]->()
            RETURN type(r) as rel_type, count(r) as count
            ORDER BY count DESC
        """)
        
        rel_stats = {}
        for record in result.records:
            rel_type = record["rel_type"]
            count = record["count"]
            rel_stats[rel_type] = count
        
        return rel_stats
    
    async def _estimate_database_size(self) -> float:
        """Estimate database size in MB"""
        try:
            # This is a rough estimation - actual size may vary
            result = await self.driver.execute_query("""
                CALL apoc.meta.stats()
                YIELD nodeCount, relCount, labelCount, relTypeCount, propertyKeyCount
                RETURN nodeCount, relCount, labelCount, relTypeCount, propertyKeyCount
            """)
            
            if result.records:
                record = result.records[0]
                # Rough estimation: ~1KB per node + relationship on average
                estimated_size = (record["nodeCount"] + record["relCount"]) * 1024 / (1024 * 1024)
                return round(estimated_size, 2)
        except:
            pass
        
        return 0.0  # Fallback if APOC not available
    
    async def _get_group_time_range(self, group_id: str) -> Dict[str, str]:
        """Get creation time range for a group"""
        try:
            result = await self.driver.execute_query("""
                MATCH (n)
                WHERE n.group_id = $group_id AND n.created_at IS NOT NULL
                RETURN min(n.created_at) as earliest, max(n.created_at) as latest
            """, group_id=group_id)
            
            if result.records and result.records[0]["earliest"]:
                return {
                    "earliest": str(result.records[0]["earliest"]),
                    "latest": str(result.records[0]["latest"])
                }
        except:
            pass
        
        return {"earliest": "unknown", "latest": "unknown"}
    
    async def _get_group_document_info(self, group_id: str) -> Dict[str, Any]:
        """Try to extract document information for a group"""
        try:
            # Look for document metadata
            result = await self.driver.execute_query("""
                MATCH (n:Document)
                WHERE n.group_id = $group_id
                RETURN n.title as title, n.document_type as doc_type, 
                       n.total_sections as sections, n.checksum as checksum
                LIMIT 1
            """, group_id=group_id)
            
            if result.records:
                record = result.records[0]
                return {
                    "title": record.get("title", "unknown"),
                    "document_type": record.get("doc_type", "unknown"),
                    "total_sections": record.get("sections", 0),
                    "checksum": record.get("checksum", "unknown")
                }
        except:
            pass
        
        return {"title": "unknown", "document_type": "unknown"}
    
    def _print_database_stats(self, stats: Dict[str, Any]):
        """Print formatted database statistics"""
        print(f"\n📊 Database Statistics")
        print(f"   📅 Generated: {stats['timestamp']}")
        print(f"   🔢 Total Nodes: {stats['total_nodes']:,}")
        print(f"   🔗 Total Relationships: {stats['total_relationships']:,}")
        print(f"   📁 Total Groups: {stats['total_groups']}")
        
        if stats.get('database_size_mb', 0) > 0:
            print(f"   💾 Estimated Size: {stats['database_size_mb']:.1f} MB")
        
        print(f"\n   📋 Node Labels:")
        for label, count in stats['node_labels'].items():
            print(f"      {label}: {count:,}")
        
        print(f"\n   🔗 Relationship Types:")
        for rel_type, count in stats['relationship_types'].items():
            print(f"      {rel_type}: {count:,}")
        
        if stats['group_ids']:
            print(f"\n   📁 Group IDs:")
            for group_id in stats['group_ids']:
                print(f"      {group_id}")
    
    def _print_group_stats(self, stats: Dict[str, Any]):
        """Print formatted group statistics"""
        print(f"\n📊 Group Statistics: {stats['group_id']}")
        print(f"   📅 Generated: {stats['timestamp']}")
        print(f"   🔢 Total Nodes: {stats['total_nodes']:,}")
        print(f"   🔗 Total Relationships: {stats['total_relationships']:,}")
        
        if stats['document_info']['title'] != "unknown":
            print(f"\n   📄 Document Info:")
            print(f"      Title: {stats['document_info']['title']}")
            print(f"      Type: {stats['document_info']['document_type']}")
            if stats['document_info'].get('total_sections'):
                print(f"      Sections: {stats['document_info']['total_sections']}")
        
        if stats['time_range']['earliest'] != "unknown":
            print(f"\n   ⏰ Time Range:")
            print(f"      Earliest: {stats['time_range']['earliest']}")
            print(f"      Latest: {stats['time_range']['latest']}")
        
        if stats['node_labels']:
            print(f"\n   📋 Node Labels:")
            for label, count in stats['node_labels'].items():
                print(f"      {label}: {count:,}")
        
        if stats['relationship_types']:
            print(f"\n   🔗 Relationship Types:")
            for rel_type, count in stats['relationship_types'].items():
                print(f"      {rel_type}: {count:,}")


async def main():
    """Main function with command-line interface"""
    import argparse
    from dotenv import load_dotenv
    
    load_dotenv()
    neo4j_uri = os.getenv('NEO4J_URI', 'bolt://localhost:7687')
    neo4j_user = os.getenv('NEO4J_USER', 'neo4j')
    neo4j_password = os.getenv('NEO4J_PASSWORD', 'password')
    
    parser = argparse.ArgumentParser(description='Graphiti Database Management Tool')
    parser.add_argument('--neo4j-uri', default=neo4j_uri, help='Neo4j URI')
    parser.add_argument('--neo4j-user', default=neo4j_user, help='Neo4j username')
    parser.add_argument('--neo4j-password', default=neo4j_password, help='Neo4j password')
    
    # Action subcommands
    subparsers = parser.add_subparsers(dest='action', help='Available actions')
    
    # Delete entire database
    delete_all_parser = subparsers.add_parser('delete-all', help='Delete entire database content')
    delete_all_parser.add_argument('--confirm', action='store_true', help='Confirm deletion')
    delete_all_parser.add_argument('--json', action='store_true', help='Output as JSON')
    
    # List group IDs
    list_groups_parser = subparsers.add_parser('list-groups', help='List all group IDs')
    list_groups_parser.add_argument('--json', action='store_true', help='Output as JSON')
    
    # Delete specific groups
    delete_groups_parser = subparsers.add_parser('delete-groups', help='Delete specific group IDs')
    delete_groups_parser.add_argument('group_ids', nargs='+', help='Group IDs to delete')
    delete_groups_parser.add_argument('--confirm', action='store_true', help='Confirm deletion')
    delete_groups_parser.add_argument('--json', action='store_true', help='Output as JSON')
    
    # Database statistics
    db_stats_parser = subparsers.add_parser('db-stats', help='Get database statistics')
    db_stats_parser.add_argument('--json', action='store_true', help='Output as JSON')
    
    # Group statistics
    group_stats_parser = subparsers.add_parser('group-stats', help='Get group statistics')
    group_stats_parser.add_argument('group_id', help='Group ID to analyze')
    group_stats_parser.add_argument('--json', action='store_true', help='Output as JSON')
    
    # Export group data
    export_parser = subparsers.add_parser('export-group', help='Export group data to JSON')
    export_parser.add_argument('group_id', help='Group ID to export')
    export_parser.add_argument('--output', help='Output file path')
    export_parser.add_argument('--json', action='store_true', help='Output result as JSON')
    
    args = parser.parse_args()
    
    if not args.action:
        parser.print_help()
        return
    
    # Initialize Graphiti
    print("🔌 Connecting to Graphiti database...")
    graphiti = Graphiti(args.neo4j_uri, args.neo4j_user, args.neo4j_password)
    
    try:
        manager = GraphitiAdminManager(graphiti)
        
        if args.action == 'delete-all':
            result = await manager.delete_entire_database(confirm=args.confirm)
            if getattr(args, 'json', False):
                print(json.dumps(result, indent=2))
        
        elif args.action == 'list-groups':
            group_ids = await manager.get_all_group_ids()
            if getattr(args, 'json', False):
                print(json.dumps({"group_ids": group_ids}, indent=2))
        
        elif args.action == 'delete-groups':
            result = await manager.delete_group_ids(args.group_ids, confirm=args.confirm)
            if getattr(args, 'json', False):
                print(json.dumps(result, indent=2))
        
        elif args.action == 'db-stats':
            stats = await manager.get_database_statistics(verbose=not getattr(args, 'json', False))
            if getattr(args, 'json', False):
                print(json.dumps(stats, indent=2))
        
        elif args.action == 'group-stats':
            stats = await manager.get_group_statistics(args.group_id, verbose=not getattr(args, 'json', False))
            if getattr(args, 'json', False):
                print(json.dumps(stats, indent=2))
        
        elif args.action == 'export-group':
            result = await manager.export_group_data(args.group_id, args.output)
            if getattr(args, 'json', False):
                print(json.dumps(result, indent=2))
    
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)
    
    finally:
        await graphiti.close()
        print("\n🔐 Connection closed")


if __name__ == "__main__":
    # Example usage:
    #
    # List all groups:
    # python graphiti_db_manager.py list-groups --neo4j-password your_password
    #
    # Get database statistics:
    # python graphiti_db_manager.py db-stats --neo4j-password your_password
    #
    # Get group statistics:
    # python graphiti_db_manager.py group-stats doc_protocol_v1 --neo4j-password your_password
    #
    # Delete specific groups:
    # python graphiti_db_manager.py delete-groups doc_old_protocol doc_test --confirm --neo4j-password your_password
    #
    # Export group data:
    # python graphiti_db_manager.py export-group doc_protocol_v1 --output protocol_backup.json --neo4j-password your_password
    #
    # Delete entire database (DANGEROUS):
    # python graphiti_db_manager.py delete-all --confirm --neo4j-password your_password
    #
    # Get JSON output:
    # python graphiti_db_manager.py db-stats --json --neo4j-password your_password
    # python graphiti_db_manager.py list-groups --json --neo4j-password your_password
    
    asyncio.run(main())
