import os
import json
from datetime import datetime, timed<PERSON><PERSON>
from typing import Any, Dict, Optional

from graphiti_core import Graphiti
from aria.memory.exceptions import GroupNotFoundError
from aria.memory.graphiti_base import _GraphitiBase

class GraphitiRuntimeManager(_GraphitiBase):
    """
    Utility for managing communities in a Graphiti knowledge graph.
    Handles incremental and periodic full community rebuilds.
    Persists state to disk so rebuild intervals are tracked across restarts.
    """
    def __init__(self, graphiti: Graphiti, state_path: Optional[str] = None):
        super().__init__(graphiti)
        # Try to infer group_id for per-project state, fallback to global
        group_id = getattr(graphiti, 'group_id', None) or getattr(graphiti, 'default_group_id', None)
        if state_path is None:
            if group_id:
                self.state_path = f'.graphiti_manager_state_{group_id}.json'
            else:
                self.state_path = '.graphiti_manager_state.json'
        else:
            self.state_path = state_path
        self.episodes_since_rebuild = 0
        self.last_rebuild = datetime.now()
        self._load_state()

    def _load_state(self):
        if os.path.exists(self.state_path):
            try:
                with open(self.state_path, 'r') as f:
                    state = json.load(f)
                self.episodes_since_rebuild = state.get('episodes_since_rebuild', 0)
                last_rebuild_str = state.get('last_rebuild')
                if last_rebuild_str:
                    self.last_rebuild = datetime.fromisoformat(last_rebuild_str)
            except Exception as e:
                print(f"[GraphitiManager] Failed to load state: {e}. Using defaults.")

    def _save_state(self):
        try:
            with open(self.state_path, 'w') as f:
                json.dump({
                    'episodes_since_rebuild': self.episodes_since_rebuild,
                    'last_rebuild': self.last_rebuild.isoformat()
                }, f)
        except Exception as e:
            print(f"[GraphitiManager] Failed to save state: {e}")

    async def add_episode_with_smart_communities(self, episode_data: Dict[str, Any], update_communities: bool = True):
        """
        Add an episode, updating communities incrementally (recommended for regular usage).
        Triggers a full rebuild if thresholds are met.
        """
        await self.graphiti.add_episode(
            **episode_data,
            update_communities=update_communities
        )
        if update_communities:
            self.episodes_since_rebuild += 1
            if self.should_rebuild_communities():
                await self.rebuild_communities()

    def should_rebuild_communities(self) -> bool:
        """
        Decide whether to trigger a full community rebuild.
        Rebuild if:
        - 100+ incremental updates since last rebuild, or
        - 7+ days since last rebuild
        """
        time_threshold = datetime.now() - self.last_rebuild > timedelta(days=7)
        count_threshold = self.episodes_since_rebuild >= 100
        return time_threshold or count_threshold

    async def rebuild_communities(self, group_ids: list[str]| None=None):
        await self.graphiti.build_communities(group_ids=group_ids)
        self.episodes_since_rebuild = 0
        self.last_rebuild = datetime.now()
        self._save_state()

    async def force_rebuild_communities(self, group_ids: list[str]| None=None):
        """
        Manually trigger a full community rebuild regardless of thresholds.
        """
        await self.rebuild_communities(group_ids=group_ids)

    async def initial_build(self, group_ids: list[str]| None=None):
        """
        Build communities after initial data ingestion.
        """
        await self.graphiti.build_communities(group_ids=group_ids)
        self.last_rebuild = datetime.now()
        self.episodes_since_rebuild = 0

    async def check_group_id(self, group_ids: list[str]):
        for group_id in group_ids:
            # This Cypher checks for any node or rel with the group_id
            result = await self.graphiti.driver.execute_query(
                '''
                MATCH (n) WHERE n.group_id = $group_id RETURN 1 AS exists LIMIT 1
                UNION
                MATCH ()-[r]-() WHERE r.group_id = $group_id RETURN 1 AS exists LIMIT 1
                ''',
                group_id=group_id
            )
            if not result.records:
                raise GroupNotFoundError(f"Group ID '{group_id}' does not exist in the database.")

    async def search(self, query: str, group_ids: list[str]| None=None):
        # Check group existence before searching
        if group_ids:
            await self.check_group_id(group_ids)
        return await self.graphiti.search_(query, group_ids=group_ids)