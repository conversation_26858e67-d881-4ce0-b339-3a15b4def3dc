from langchain_openai import ChatOpenAI
from pydantic import BaseModel, <PERSON>
from typing import List, Dict, Any, Literal, Optional, TypedDict, Annotated
from langgraph.graph import StateGraph, START, END
import operator
from langchain_core.tools import tool
from langchain_core.messages import HumanMessage, SystemMessage
import json
import os

# Initialize LLM
llm = ChatOpenAI(model="gpt-4.1-mini")

# Define data models for different rule shapes
class ScalarFactModel(BaseModel):
    entity_type: str = Field(description="Type of entity being measured (e.g., NOAEL)")
    subject: str = Field(description="Subject of measurement (e.g., male rats)")
    value: Optional[float] = Field(None, description="Numerical value")
    unit: Optional[str] = Field(None, description="Unit of measurement")
    confidence: float = Field(description="Confidence in extraction (0-1)")
    source_location: str = Field(description="Document/section/page where found")
    context: str = Field(description="Surrounding text for verification")

class DocumentMetaModel(BaseModel):
    property_name: str = Field(description="Document property being queried")
    value: str = Field(description="Value of the property")
    confidence: float = Field(description="Confidence in extraction (0-1)")
    source_location: str = Field(description="Where in document this was found")

class ConditionalPolicyModel(BaseModel):
    condition: str = Field(description="The condition being evaluated")
    condition_met: bool = Field(description="Whether condition is met")
    policy_requirement: str = Field(description="The policy requirement to check")
    requirement_met: Optional[bool] = Field(None, description="Whether requirement is satisfied")
    overall_compliant: Optional[bool] = Field(None, description="Overall compliance status")
    justification: str = Field(description="Reasoning for the compliance determination")
    source_locations: List[str] = Field(description="Documents/sections used for assessment")

# Query router schema for classifying queries
class QueryClassification(BaseModel):
    rule_shape: Literal["scalar_fact", "document_meta", "conditional_policy", 
                         "computed_aggregate", "semantic_judgement"]
    entities: List[str] = Field(description="Key entities mentioned in the query")
    conditions: Optional[List[str]] = Field(None, description="Conditions (for conditional queries)")
    search_terms: List[str] = Field(description="Terms to use for document search")

# Define tools
@tool
def search_database(query: str, rule_shape: str) -> List[Dict]:
    """Search the database for previously extracted data matching the query."""
    # In production, this would query a vector DB or structured DB
    return []  # Placeholder for demo

@tool
def get_document_index(project_path: str) -> Dict:
    """Retrieve the navigation map (index.json) for the document corpus."""
    index_path = os.path.join(project_path, "index.json")
    try:
        with open(index_path, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Error: index.json not found at {index_path}")
        return {}
    except json.JSONDecodeError:
        print(f"Error: Could not decode index.json at {index_path}")
        return {}

@tool
def retrieve_document_section(project_path: str, doc_id: str, section_id: str) -> str:
    """Retrieve content from a specific document section."""
    # section_id is logged/passed along but does not change which file is read.
    # doc_id is assumed to be the base name of a markdown file in the "reports" subdirectory.
    file_path = os.path.join(project_path, "reports", f"{doc_id}.md")
    try:
        with open(file_path, 'r') as f:
            return f.read()
    except FileNotFoundError:
        print(f"Error: Document file not found at {file_path}")
        return f"Document file not found: {file_path}"
    except Exception as e:
        print(f"Error reading document file at {file_path}: {e}")
        return f"Error reading document file: {file_path}"

# State definition for the workflow
class ARIAState(TypedDict, total=False):
    query: str  # Original user query
    query_classification: Optional[Dict]  # Classified query information
    rule_shape: Optional[str]  # The determined rule shape
    relevant_documents: Optional[List[Dict]]  # Identified relevant documents
    document_chunks: Optional[List[str]]  # Retrieved document content
    extracted_data: Optional[Dict]  # Extracted information
    db_results: Optional[List[Dict]]  # Results from database search
    final_answer: Optional[Dict]  # Final formatted answer
    error: Optional[str]  # Any error information
    project_path: Optional[str] # Path to the project data
    
    # For tracking document exploration progress
    documents_to_explore: Annotated[List[Dict], operator.add]
    explored_documents: Annotated[List[Dict], operator.add]

# Worker state for document exploration
class ExplorationState(TypedDict):
    document: Dict
    project_path: str # Path to the project data
    rule_shape: str # Rule shape for extraction
    document_content: str
    extracted_info: Dict
    explored_documents: Annotated[List[Dict], operator.add]

# Create augmented LLMs for specific tasks
query_classifier = llm.with_structured_output(QueryClassification)
scalar_extractor = llm.with_structured_output(ScalarFactModel)
document_meta_extractor = llm.with_structured_output(DocumentMetaModel)
conditional_extractor = llm.with_structured_output(ConditionalPolicyModel)

# Dispatcher for extractors by rule_shape
EXTRACTORS = {
    "scalar_fact": scalar_extractor,
    "document_meta": document_meta_extractor,
    "conditional_policy": conditional_extractor,
    # Add more as needed
}

# 1. Query classification node
def classify_query(state: ARIAState) -> ARIAState:
    console = Console()
    console.print("🪄 [ARIA TRACE] 3. Classifying query and extracting entities...")
    """Classify the input query into a rule shape and extract key information."""
    query = state.get("query", "")
    
    classification = query_classifier.invoke(
        [
            SystemMessage(content="""You are a scientific document query classifier.
            Analyze the query and determine which rule shape it belongs to."""),
            HumanMessage(content=f"Classify this query: {query}")
        ]
    )
    
    return {
        "query_classification": classification.dict(),
        "rule_shape": classification.rule_shape
    }

# 2. Database search node
def search_existing_data(state: ARIAState) -> ARIAState:
    console = Console()
    console.print("🪄 [ARIA TRACE] 4. Searching database for existing answers...")
    """Check if data has already been extracted and stored in the database."""
    if "query_classification" not in state:
        # Return a valid ARIAState with error field
        return {"error": "Query not classified", "db_results": []}
    
    query_info = state.get("query_classification", {})
    search_results = search_database(
        " ".join(query_info.get("search_terms", [])), 
        query_info.get("rule_shape", "")
    )
    
    return {"db_results": search_results}

# 3. Document identification node
def identify_relevant_documents(state: ARIAState) -> ARIAState:
    console = Console()
    console.print("🪄 [ARIA TRACE] 5. Identifying relevant documents...")
    """Identify relevant documents and sections based on the query."""
    query_info = state.get("query_classification")
    project_path = state.get("project_path")
    if not project_path:
        return {"error": "Project path not set in ARIAState", "relevant_documents": [], "documents_to_explore": []}

    if not isinstance(query_info, dict):
        query_info = {}
    doc_index = get_document_index(project_path)
    
    # Prompt LLM to identify relevant documents and sections
    prompt = f"""
    Based on this query: {state.get('query', '')}
    And these entities: {query_info.get('entities', [])}
    
    Find relevant documents and sections in this index:
    {json.dumps(doc_index, indent=2)}
    
    Return only the document IDs and section IDs that are most relevant.
    """
    
    relevant_docs_response = llm.invoke(prompt)
    
    # In production, this would parse the LLM response properly
    # For the demo, we'll use a placeholder
    relevant_docs = [
        {"doc_id": "tox_report_2023", "section_id": "section_3.4"}
    ]
    if not relevant_docs:
        return {
            "relevant_documents": [],
            "documents_to_explore": []
        }
    return {
        "relevant_documents": relevant_docs,
        "documents_to_explore": relevant_docs
    }

# 4. Document exploration node (agent-like behavior)
def explore_document(state: ExplorationState) -> ExplorationState:
    console = Console()
    console.print("🪄 [ARIA TRACE] 6. Invoking LangChain agent for document exploration...")
    """Explore a document to extract relevant information."""
    doc = state.get("document", {})
    project_path = state.get("project_path")
    rule_shape = state.get("rule_shape") # rule_shape is now directly in ExplorationState

    if not project_path:
        # This should ideally not happen if assign_document_explorers sets it
        return {"error": "Project path not set in ExplorationState", "explored_documents": [doc]}

    doc_content = retrieve_document_section(project_path, doc.get("doc_id", ""), doc.get("section_id", ""))
    
    extractor = EXTRACTORS.get(rule_shape, scalar_extractor)  # Default to scalar_extractor if unknown
    
    # Prompt should match the expected format for each extractor
    extract_prompt = f"""
    Extract information from this document section:
    {doc_content}
    
    Return structured data following the {rule_shape if rule_shape else 'ScalarFactModel'} format.
    """
    
    extracted_info = extractor.invoke(extract_prompt)
    
    return {
        "document_content": doc_content,
        "extracted_info": extracted_info.dict(),
        "explored_documents": [doc]
    }

# 5. Answer formulation node
def formulate_answer(state: ARIAState) -> ARIAState:
    console = Console()
    console.print("🪄 [ARIA TRACE] 7. Formulating final answer...")
    """Formulate the final answer based on extracted data."""
    rule_shape = state.get("rule_shape", "")
    
    # If we found data in the database
    db_results = state.get("db_results", [])
    if db_results and len(db_results) > 0:
        return {"final_answer": db_results[0]}
    
    # Otherwise use the extracted data
    explored_documents = state.get("explored_documents", [])
    if explored_documents:
        # Aggregate all extracted info
        extracted_data = [doc.get("extracted_info") for doc in explored_documents]
        
        # Format based on rule shape
        if rule_shape == "scalar_fact":
            # For scalar facts, we might want to aggregate multiple values
            answer = {
                "answer_type": "scalar_fact",
                "data": extracted_data,
                "sources": [doc.get("doc_id") for doc in explored_documents]
            }
        else:
            # Handle other rule shapes
            answer = {
                "answer_type": rule_shape,
                "data": extracted_data,
                "sources": [doc.get("doc_id") for doc in explored_documents]
            }
        
        return {"final_answer": answer}
    
    return {"error": "No data found"}

# Decision functions
def check_db_results(state: ARIAState) -> str:
    """Check if we found results in the database."""
    db_results = state.get("db_results", [])
    if db_results and len(db_results) > 0:
        return "found_in_db"
    return "not_in_db"

from langgraph.types import Send

def assign_document_explorers(state: ARIAState):
    """Set up parallel document exploration. If no docs, route to formulate_answer as fallback."""
    docs = state.get("documents_to_explore", [])
    project_path = state.get("project_path")
    rule_shape = state.get("rule_shape") # Get rule_shape from ARIAState

    if not project_path:
        # This indicates an issue, as project_path should be set.
        # Depending on desired robustness, could raise error or try to proceed without it.
        # For now, let's assume it's an error condition for exploration.
        print("Error: project_path not available in assign_document_explorers.")
        # Potentially route to an error handling node or just stop this path.
        # For now, sending an empty list to avoid breaking the graph structure,
        # but this means exploration won't happen as intended.
        return [Send("formulate_answer", {})] # Or handle error appropriately

    if not rule_shape:
        print("Warning: rule_shape not available in assign_document_explorers. Defaulting for explorers.")
        # Default or handle as an error. For now, let explorers use their default.
        # This might be an issue if extractors strictly need a rule_shape.

    if not docs:
        # Fallback: skip exploration and go straight to answer formulation
        # Ensure formulate_answer can handle this scenario (e.g. no explored_documents)
        return [Send("formulate_answer", {})] # Sending to formulate_answer, as it's a terminal path.

    # Pass document, project_path, and rule_shape to each explorer
    sends = []
    for doc in docs:
        explorer_payload = {
            "document": doc,
            "project_path": project_path,
            "rule_shape": rule_shape  # Pass the rule_shape from ARIAState
        }
        sends.append(Send("explore_document", explorer_payload))
    return sends

# Aggregation node to merge results from parallel explorers

def merge_results(state: ARIAState) -> ARIAState:
    """Aggregate explored_documents from all parallel ExplorationStates into ARIAState."""
    all_explored_docs = []
    parallel_results = state.get("parallel_results", [])
    for s in parallel_results:
        if isinstance(s, dict) and s.get("explored_documents"):
            all_explored_docs.extend(s["explored_documents"])
    state = dict(state)
    state["explored_documents"] = all_explored_docs
    return state

# Build the workflow
aria_workflow = StateGraph(ARIAState)

# Add nodes
aria_workflow.add_node("classify_query", classify_query)
aria_workflow.add_node("search_existing_data", search_existing_data)
aria_workflow.add_node("identify_documents", identify_relevant_documents)
aria_workflow.add_node("explore_document", explore_document)
aria_workflow.add_node("merge_results", merge_results)
aria_workflow.add_node("formulate_answer", formulate_answer)

# Add edges
aria_workflow.add_edge(START, "classify_query")
aria_workflow.add_edge("classify_query", "search_existing_data")
aria_workflow.add_conditional_edges(
    "search_existing_data",
    check_db_results,
    {
        "found_in_db": "formulate_answer",
        "not_in_db": "identify_documents"
    }
)
aria_workflow.add_conditional_edges(
    "identify_documents", 
    assign_document_explorers,
    ["explore_document"]
)
aria_workflow.add_edge("explore_document", "merge_results")
aria_workflow.add_edge("merge_results", "formulate_answer")
aria_workflow.add_edge("formulate_answer", END)

# Compile the workflow
aria_app = aria_workflow.compile()

def run_agent_query(query: str, project_path: str) -> dict:
    """
    Run the ARIA agent workflow for a given query and project path.
    Returns the workflow result dictionary.
    """
    return aria_app.invoke({
        "query": query,
        "project_path": project_path,
        "documents_to_explore": [],
        "explored_documents": []
    })

if __name__ == "__main__":
    # Demo usage: only runs when this file is executed directly
    query = "What is the NOAEL for male rats?"
    project_path = "data/example_content_protocol"  # Example path
    result = run_agent_query(query, project_path)
    print(f"Query: {query}")
    print(f"Final answer: {result.get('final_answer')}")