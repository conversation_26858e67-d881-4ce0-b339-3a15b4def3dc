import os
from graphiti_core import <PERSON><PERSON><PERSON><PERSON>

def get_graphiti_client():
    """
    Centralized Graphiti DB client factory.
    Reads connection details from environment variables:
    - NEO4J_URI
    - NEO4J_USER
    - NEO4J_PASSWORD
    Defaults to local Neo4j if not set.
    """
    uri = os.getenv("NEO4J_URI", "bolt://localhost:7687")
    user = os.getenv("NEO4J_USER", "neo4j")
    password = os.getenv("NEO4J_PASSWORD", "password")
    return Graphiti(uri=uri, user=user, password=password)
