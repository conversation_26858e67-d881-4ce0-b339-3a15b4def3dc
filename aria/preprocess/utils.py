"""
Utility functions for ARIA preprocessing pipeline.

This module provides various utility functions for PDF processing, OpenAI API interactions,
JSON handling, and document structure manipulation.
"""

import asyncio
import copy
import json
import logging
import os
import re
import time
import yaml
from datetime import datetime
from io import BytesIO
from pathlib import Path
from types import SimpleNamespace as config
from typing import Any, Dict, List, Optional, Tuple, Union

from dotenv import load_dotenv

# Third-party imports with error handling
try:
    import tiktoken
except ImportError:
    tiktoken = None

try:
    import openai
except ImportError:
    openai = None

try:
    import PyPDF2
except ImportError:
    PyPDF2 = None

try:
    import pymupdf
except ImportError:
    pymupdf = None

load_dotenv()

# Configuration
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")


def count_tokens(text: str, model: str) -> int:
    """
    Count the number of tokens in a text string for a given model.

    Args:
        text: The text to count tokens for
        model: The model name to use for tokenization

    Returns:
        Number of tokens in the text

    Raises:
        ValueError: If tiktoken is not available or model is not supported
    """
    if tiktoken is None:
        raise ValueError("tiktoken is not installed. Please install it to use token counting.")

    if not text or not isinstance(text, str):
        return 0

    if model.startswith('gpt-4.1'):
        model = 'gpt-4o'

    try:
        enc = tiktoken.encoding_for_model(model)
        tokens = enc.encode(text)
        return len(tokens)
    except Exception as e:
        logging.error(f"Error counting tokens for model {model}: {e}")
        raise ValueError(f"Failed to count tokens for model {model}: {e}")


def ChatGPT_API_with_finish_reason(
    model: str,
    prompt: str,
    api_key: Optional[str] = None,
    chat_history: Optional[List[Dict[str, str]]] = None
) -> Tuple[str, str]:
    """
    Call OpenAI API and return response with finish reason.

    Args:
        model: The model to use
        prompt: The prompt to send
        api_key: OpenAI API key (defaults to OPENAI_API_KEY env var)
        chat_history: Previous chat messages

    Returns:
        Tuple of (response_content, finish_reason)

    Raises:
        ValueError: If OpenAI is not available or API key is missing
    """
    if openai is None:
        raise ValueError("openai is not installed. Please install it to use OpenAI API.")

    if api_key is None:
        api_key = OPENAI_API_KEY

    if not api_key:
        raise ValueError("OpenAI API key is required. Set OPENAI_API_KEY environment variable.")

    if not prompt or not isinstance(prompt, str):
        raise ValueError("Prompt must be a non-empty string.")

    max_retries = 10
    client = openai.OpenAI(api_key=api_key)

    for i in range(max_retries):
        try:
            # Create a copy of chat_history to avoid modifying the original
            if chat_history:
                messages = chat_history.copy()
                messages.append({"role": "user", "content": prompt})
            else:
                messages = [{"role": "user", "content": prompt}]

            response = client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=0,
            )

            if response.choices[0].finish_reason == "length":
                return response.choices[0].message.content, "max_output_reached"
            else:
                return response.choices[0].message.content, "finished"

        except Exception as e:
            logging.error(f"OpenAI API error (attempt {i+1}/{max_retries}): {e}")
            if i < max_retries - 1:
                time.sleep(1)  # Wait for 1 second before retrying
            else:
                logging.error(f'Max retries reached for prompt: {prompt[:100]}...')
                return "Error", "error"



def ChatGPT_API(
    model: str,
    prompt: str,
    api_key: Optional[str] = None,
    chat_history: Optional[List[Dict[str, str]]] = None
) -> str:
    """
    Call OpenAI API and return response content.

    Args:
        model: The model to use
        prompt: The prompt to send
        api_key: OpenAI API key (defaults to OPENAI_API_KEY env var)
        chat_history: Previous chat messages

    Returns:
        Response content from the API

    Raises:
        ValueError: If OpenAI is not available or API key is missing
    """
    if openai is None:
        raise ValueError("openai is not installed. Please install it to use OpenAI API.")

    if api_key is None:
        api_key = OPENAI_API_KEY

    if not api_key:
        raise ValueError("OpenAI API key is required. Set OPENAI_API_KEY environment variable.")

    if not prompt or not isinstance(prompt, str):
        raise ValueError("Prompt must be a non-empty string.")

    max_retries = 10
    client = openai.OpenAI(api_key=api_key)

    for i in range(max_retries):
        try:
            # Create a copy of chat_history to avoid modifying the original
            if chat_history:
                messages = chat_history.copy()
                messages.append({"role": "user", "content": prompt})
            else:
                messages = [{"role": "user", "content": prompt}]

            response = client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=0,
            )

            return response.choices[0].message.content
        except Exception as e:
            logging.error(f"OpenAI API error (attempt {i+1}/{max_retries}): {e}")
            if i < max_retries - 1:
                time.sleep(1)  # Wait for 1 second before retrying
            else:
                logging.error(f'Max retries reached for prompt: {prompt[:100]}...')
                return "Error"


async def ChatGPT_API_async(
    model: str,
    prompt: str,
    api_key: Optional[str] = None
) -> str:
    """
    Async call to OpenAI API and return response content.

    Args:
        model: The model to use
        prompt: The prompt to send
        api_key: OpenAI API key (defaults to OPENAI_API_KEY env var)

    Returns:
        Response content from the API

    Raises:
        ValueError: If OpenAI is not available or API key is missing
    """
    if openai is None:
        raise ValueError("openai is not installed. Please install it to use OpenAI API.")

    if api_key is None:
        api_key = OPENAI_API_KEY

    if not api_key:
        raise ValueError("OpenAI API key is required. Set OPENAI_API_KEY environment variable.")

    if not prompt or not isinstance(prompt, str):
        raise ValueError("Prompt must be a non-empty string.")

    max_retries = 10
    client = openai.AsyncOpenAI(api_key=api_key)

    for i in range(max_retries):
        try:
            messages = [{"role": "user", "content": prompt}]
            response = await client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=0,
            )
            return response.choices[0].message.content
        except Exception as e:
            logging.error(f"OpenAI API error (attempt {i+1}/{max_retries}): {e}")
            if i < max_retries - 1:
                await asyncio.sleep(1)  # Wait for 1 second before retrying
            else:
                logging.error(f'Max retries reached for prompt: {prompt[:100]}...')
                return "Error"
            
def get_json_content(response: str) -> str:
    """
    Extract JSON content from a response string that may contain markdown code blocks.

    Args:
        response: The response string potentially containing JSON

    Returns:
        Cleaned JSON content string
    """
    if not response or not isinstance(response, str):
        return ""

    start_idx = response.find("```json")
    if start_idx != -1:
        start_idx += 7
        response = response[start_idx:]

    end_idx = response.rfind("```")
    if end_idx != -1:
        response = response[:end_idx]

    json_content = response.strip()
    return json_content


def extract_json(content: str) -> Dict[str, Any]:
    """
    Extract and parse JSON from content string with error handling and cleanup.

    Args:
        content: String content that may contain JSON

    Returns:
        Parsed JSON as dictionary, empty dict if parsing fails
    """
    if not content or not isinstance(content, str):
        return {}

    try:
        # First, try to extract JSON enclosed within ```json and ```
        start_idx = content.find("```json")
        if start_idx != -1:
            start_idx += 7  # Adjust index to start after the delimiter
            end_idx = content.rfind("```")
            json_content = content[start_idx:end_idx].strip()
        else:
            # If no delimiters, assume entire content could be JSON
            json_content = content.strip()

        # Clean up common issues that might cause parsing errors
        json_content = json_content.replace('None', 'null')  # Replace Python None with JSON null
        json_content = json_content.replace('\n', ' ').replace('\r', ' ')  # Remove newlines
        json_content = ' '.join(json_content.split())  # Normalize whitespace

        # Attempt to parse and return the JSON object
        return json.loads(json_content)
    except json.JSONDecodeError as e:
        logging.error(f"Failed to extract JSON: {e}")
        # Try to clean up the content further if initial parsing fails
        try:
            # Remove any trailing commas before closing brackets/braces
            json_content = json_content.replace(',]', ']').replace(',}', '}')
            return json.loads(json_content)
        except json.JSONDecodeError:
            logging.error("Failed to parse JSON even after cleanup")
            return {}
    except Exception as e:
        logging.error(f"Unexpected error while extracting JSON: {e}")
        return {}

def write_node_id(data: Union[Dict[str, Any], List[Any]], node_id: int = 0) -> int:
    """
    Recursively assign node IDs to a nested data structure.

    Args:
        data: The data structure to assign node IDs to
        node_id: Starting node ID (default: 0)

    Returns:
        Next available node ID
    """
    if isinstance(data, dict):
        data['node_id'] = str(node_id).zfill(4)
        node_id += 1
        for key in list(data.keys()):
            if 'nodes' in key:
                node_id = write_node_id(data[key], node_id)
    elif isinstance(data, list):
        for index in range(len(data)):
            node_id = write_node_id(data[index], node_id)
    return node_id


def get_nodes(structure: Union[Dict[str, Any], List[Any]]) -> List[Dict[str, Any]]:
    """
    Extract all nodes from a nested structure, excluding child nodes.

    Args:
        structure: The nested structure to extract nodes from

    Returns:
        List of nodes without their children
    """
    if isinstance(structure, dict):
        structure_node = copy.deepcopy(structure)
        structure_node.pop('nodes', None)
        nodes = [structure_node]
        for key in list(structure.keys()):
            if 'nodes' in key:
                nodes.extend(get_nodes(structure[key]))
        return nodes
    elif isinstance(structure, list):
        nodes = []
        for item in structure:
            nodes.extend(get_nodes(item))
        return nodes
    return []


def structure_to_list(structure: Union[Dict[str, Any], List[Any]]) -> List[Dict[str, Any]]:
    """
    Convert a nested structure to a flat list of all nodes.

    Args:
        structure: The nested structure to flatten

    Returns:
        Flat list of all nodes in the structure
    """
    if isinstance(structure, dict):
        nodes = []
        nodes.append(structure)
        if 'nodes' in structure:
            nodes.extend(structure_to_list(structure['nodes']))
        return nodes
    elif isinstance(structure, list):
        nodes = []
        for item in structure:
            nodes.extend(structure_to_list(item))
        return nodes
    return []


def get_leaf_nodes(structure: Union[Dict[str, Any], List[Any]]) -> List[Dict[str, Any]]:
    """
    Extract only the leaf nodes (nodes without children) from a nested structure.

    Args:
        structure: The nested structure to extract leaf nodes from

    Returns:
        List of leaf nodes
    """
    if isinstance(structure, dict):
        if not structure.get('nodes'):
            structure_node = copy.deepcopy(structure)
            structure_node.pop('nodes', None)
            return [structure_node]
        else:
            leaf_nodes = []
            for key in list(structure.keys()):
                if 'nodes' in key:
                    leaf_nodes.extend(get_leaf_nodes(structure[key]))
            return leaf_nodes
    elif isinstance(structure, list):
        leaf_nodes = []
        for item in structure:
            leaf_nodes.extend(get_leaf_nodes(item))
        return leaf_nodes
    return []

def is_leaf_node(data: Union[Dict[str, Any], List[Any]], node_id: str) -> bool:
    """
    Check if a node with given ID is a leaf node (has no children).

    Args:
        data: The data structure to search in
        node_id: The node ID to check

    Returns:
        True if the node is a leaf node, False otherwise
    """
    def find_node(data: Union[Dict[str, Any], List[Any]], node_id: str) -> Optional[Dict[str, Any]]:
        """Helper function to find the node by its node_id"""
        if isinstance(data, dict):
            if data.get('node_id') == node_id:
                return data
            for key in data.keys():
                if 'nodes' in key:
                    result = find_node(data[key], node_id)
                    if result:
                        return result
        elif isinstance(data, list):
            for item in data:
                result = find_node(item, node_id)
                if result:
                    return result
        return None

    # Find the node with the given node_id
    node = find_node(data, node_id)

    # Check if the node is a leaf node
    if node and not node.get('nodes'):
        return True
    return False


def get_last_node(structure: List[Any]) -> Any:
    """
    Get the last node from a structure list.

    Args:
        structure: List structure to get last node from

    Returns:
        The last node in the structure

    Raises:
        IndexError: If structure is empty
    """
    if not structure:
        raise IndexError("Cannot get last node from empty structure")
    return structure[-1]


def extract_text_from_pdf(pdf_path: str) -> str:
    """
    Extract all text from a PDF file.

    Args:
        pdf_path: Path to the PDF file

    Returns:
        Extracted text from all pages

    Raises:
        ValueError: If PyPDF2 is not available or file doesn't exist
    """
    if PyPDF2 is None:
        raise ValueError("PyPDF2 is not installed. Please install it to extract PDF text.")

    if not os.path.exists(pdf_path):
        raise FileNotFoundError(f"PDF file not found: {pdf_path}")

    try:
        pdf_reader = PyPDF2.PdfReader(pdf_path)
        text = ""
        for page_num in range(len(pdf_reader.pages)):
            page = pdf_reader.pages[page_num]
            text += page.extract_text()
        return text
    except Exception as e:
        logging.error(f"Error extracting text from PDF {pdf_path}: {e}")
        raise ValueError(f"Failed to extract text from PDF: {e}")


def get_pdf_title(pdf_path: str) -> str:
    """
    Get the title from PDF metadata.

    Args:
        pdf_path: Path to the PDF file

    Returns:
        PDF title or 'Untitled' if not available

    Raises:
        ValueError: If PyPDF2 is not available or file doesn't exist
    """
    if PyPDF2 is None:
        raise ValueError("PyPDF2 is not installed. Please install it to read PDF metadata.")

    if not os.path.exists(pdf_path):
        raise FileNotFoundError(f"PDF file not found: {pdf_path}")

    try:
        pdf_reader = PyPDF2.PdfReader(pdf_path)
        meta = pdf_reader.metadata
        title = meta.title if meta and meta.title else 'Untitled'
        return title
    except Exception as e:
        logging.error(f"Error reading PDF title from {pdf_path}: {e}")
        return 'Untitled'


def get_text_of_pages(pdf_path: str, start_page: int, end_page: int, tag: bool = True) -> str:
    """
    Extract text from specific pages of a PDF.

    Args:
        pdf_path: Path to the PDF file
        start_page: Starting page number (1-indexed)
        end_page: Ending page number (1-indexed, inclusive)
        tag: Whether to add page index tags

    Returns:
        Extracted text from specified pages

    Raises:
        ValueError: If PyPDF2 is not available, file doesn't exist, or invalid page range
    """
    if PyPDF2 is None:
        raise ValueError("PyPDF2 is not installed. Please install it to extract PDF text.")

    if not os.path.exists(pdf_path):
        raise FileNotFoundError(f"PDF file not found: {pdf_path}")

    if start_page < 1 or end_page < start_page:
        raise ValueError(f"Invalid page range: {start_page}-{end_page}")

    try:
        pdf_reader = PyPDF2.PdfReader(pdf_path)
        if end_page > len(pdf_reader.pages):
            raise ValueError(f"End page {end_page} exceeds PDF length {len(pdf_reader.pages)}")

        text = ""
        for page_num in range(start_page-1, end_page):
            page = pdf_reader.pages[page_num]
            page_text = page.extract_text()
            if tag:
                text += f"<start_index_{page_num+1}>\n{page_text}\n<end_index_{page_num+1}>\n"
            else:
                text += page_text
        return text
    except Exception as e:
        logging.error(f"Error extracting text from pages {start_page}-{end_page} of {pdf_path}: {e}")
        raise ValueError(f"Failed to extract text from PDF pages: {e}")


def get_first_start_page_from_text(text: str) -> int:
    """
    Extract the first page number from text with page index tags.

    Args:
        text: Text containing page index tags

    Returns:
        First page number found, or -1 if none found
    """
    if not text or not isinstance(text, str):
        return -1

    start_page_match = re.search(r'<start_index_(\d+)>', text)
    if start_page_match:
        return int(start_page_match.group(1))
    return -1


def get_last_start_page_from_text(text: str) -> int:
    """
    Extract the last page number from text with page index tags.

    Args:
        text: Text containing page index tags

    Returns:
        Last page number found, or -1 if none found
    """
    if not text or not isinstance(text, str):
        return -1

    # Find all matches of start_index tags
    start_page_matches = re.finditer(r'<start_index_(\d+)>', text)
    # Convert iterator to list and get the last match if any exist
    matches_list = list(start_page_matches)
    if matches_list:
        return int(matches_list[-1].group(1))
    return -1


def sanitize_filename(filename: str, replacement: str = '-') -> str:
    """
    Sanitize a filename by replacing invalid characters.

    Args:
        filename: The filename to sanitize
        replacement: Character to replace invalid characters with

    Returns:
        Sanitized filename
    """
    if not filename or not isinstance(filename, str):
        return "untitled"

    # In Linux, only '/' and '\0' (null) are invalid in filenames.
    # Null can't be represented in strings, so we only handle '/'.
    return filename.replace('/', replacement)


def get_pdf_name(pdf_path: Union[str, BytesIO]) -> str:
    """
    Extract PDF name from path or BytesIO object.

    Args:
        pdf_path: Path to PDF file or BytesIO object

    Returns:
        PDF name or 'Untitled' if not available
    """
    if isinstance(pdf_path, str):
        return os.path.basename(pdf_path)
    elif isinstance(pdf_path, BytesIO):
        if PyPDF2 is None:
            return 'Untitled'
        try:
            pdf_reader = PyPDF2.PdfReader(pdf_path)
            meta = pdf_reader.metadata
            pdf_name = meta.title if meta and meta.title else 'Untitled'
            return sanitize_filename(pdf_name)
        except Exception as e:
            logging.error(f"Error reading PDF name from BytesIO: {e}")
            return 'Untitled'
    else:
        return 'Untitled'


class JsonLogger:
    """
    A logger that writes structured data to JSON files.
    """

    def __init__(self, file_path: Union[str, BytesIO]):
        """
        Initialize the JSON logger.

        Args:
            file_path: Path to the file or BytesIO object for naming
        """
        # Extract PDF name for logger name
        pdf_name = get_pdf_name(file_path)

        # Remove file extension if present
        if pdf_name.endswith('.pdf'):
            pdf_name = pdf_name[:-4]

        current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.filename = f"{pdf_name}_{current_time}.json"
        os.makedirs("./logs", exist_ok=True)
        # Initialize empty list to store all messages
        self.log_data: List[Dict[str, Any]] = []

    def log(self, level: str, message: Union[str, Dict[str, Any]], **kwargs: Any) -> None:
        """
        Log a message with the specified level.

        Args:
            level: Log level (INFO, ERROR, DEBUG, etc.)
            message: Message to log (string or dict)
            **kwargs: Additional metadata
        """
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'level': level,
        }

        if isinstance(message, dict):
            log_entry.update(message)
        else:
            log_entry['message'] = message

        # Add any additional kwargs
        log_entry.update(kwargs)

        self.log_data.append(log_entry)

        # Write entire log data to file
        try:
            with open(self._filepath(), "w") as f:
                json.dump(self.log_data, f, indent=2)
        except Exception as e:
            # Fallback to standard logging if file write fails
            logging.error(f"Failed to write to JSON log file: {e}")

    def info(self, message: Union[str, Dict[str, Any]], **kwargs: Any) -> None:
        """Log an info message."""
        self.log("INFO", message, **kwargs)

    def error(self, message: Union[str, Dict[str, Any]], **kwargs: Any) -> None:
        """Log an error message."""
        self.log("ERROR", message, **kwargs)

    def debug(self, message: Union[str, Dict[str, Any]], **kwargs: Any) -> None:
        """Log a debug message."""
        self.log("DEBUG", message, **kwargs)

    def exception(self, message: Union[str, Dict[str, Any]], **kwargs: Any) -> None:
        """Log an exception message."""
        kwargs["exception"] = True
        self.log("ERROR", message, **kwargs)

    def _filepath(self) -> str:
        """Get the full file path for the log file."""
        return os.path.join("logs", self.filename)
    



def list_to_tree(data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Convert a flat list of structured data to a hierarchical tree.

    Args:
        data: List of dictionaries with structure information

    Returns:
        Hierarchical tree structure
    """
    if not data or not isinstance(data, list):
        return []

    def get_parent_structure(structure: str) -> Optional[str]:
        """Helper function to get the parent structure code"""
        if not structure:
            return None
        parts = str(structure).split('.')
        return '.'.join(parts[:-1]) if len(parts) > 1 else None

    # First pass: Create nodes and track parent-child relationships
    nodes = {}
    root_nodes = []

    for item in data:
        structure = item.get('structure')
        if structure is None:
            continue

        node = {
            'title': item.get('title'),
            'start_index': item.get('start_index'),
            'end_index': item.get('end_index'),
            'nodes': []
        }

        nodes[structure] = node

        # Find parent
        parent_structure = get_parent_structure(structure)

        if parent_structure:
            # Add as child to parent if parent exists
            if parent_structure in nodes:
                nodes[parent_structure]['nodes'].append(node)
            else:
                root_nodes.append(node)
        else:
            # No parent, this is a root node
            root_nodes.append(node)

    def clean_node(node: Dict[str, Any]) -> Dict[str, Any]:
        """Helper function to clean empty children arrays"""
        if not node['nodes']:
            del node['nodes']
        else:
            for child in node['nodes']:
                clean_node(child)
        return node

    # Clean and return the tree
    return [clean_node(node) for node in root_nodes]


def add_preface_if_needed(data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Add a preface node if the first item doesn't start at page 1.

    Args:
        data: List of document structure data

    Returns:
        Data with preface added if needed
    """
    if not isinstance(data, list) or not data:
        return data

    first_item = data[0]
    if (first_item.get('physical_index') is not None and
        first_item['physical_index'] > 1):
        preface_node = {
            "structure": "0",
            "title": "Preface",
            "physical_index": 1,
        }
        data.insert(0, preface_node)
    return data



def get_page_tokens(
    pdf_path: Union[str, BytesIO],
    model: str = "gpt-4o-2024-11-20",
    pdf_parser: str = "PyPDF2"
) -> List[Tuple[str, int]]:
    """
    Extract text and token counts from PDF pages.

    Args:
        pdf_path: Path to PDF file or BytesIO object
        model: Model name for token counting
        pdf_parser: Parser to use ("PyPDF2" or "PyMuPDF")

    Returns:
        List of tuples (page_text, token_count)

    Raises:
        ValueError: If required libraries are not available or invalid parser
    """
    if tiktoken is None:
        raise ValueError("tiktoken is not installed. Please install it to count tokens.")

    try:
        enc = tiktoken.encoding_for_model(model)
    except Exception as e:
        raise ValueError(f"Failed to get encoding for model {model}: {e}")

    if pdf_parser == "PyPDF2":
        if PyPDF2 is None:
            raise ValueError("PyPDF2 is not installed. Please install it to use PyPDF2 parser.")

        if isinstance(pdf_path, str) and not os.path.exists(pdf_path):
            raise FileNotFoundError(f"PDF file not found: {pdf_path}")

        try:
            pdf_reader = PyPDF2.PdfReader(pdf_path)
            page_list = []
            for page_num in range(len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                page_text = page.extract_text()
                token_length = len(enc.encode(page_text))
                page_list.append((page_text, token_length))
            return page_list
        except Exception as e:
            raise ValueError(f"Error processing PDF with PyPDF2: {e}")

    elif pdf_parser == "PyMuPDF":
        if pymupdf is None:
            raise ValueError("pymupdf is not installed. Please install it to use PyMuPDF parser.")

        try:
            if isinstance(pdf_path, BytesIO):
                doc = pymupdf.open(stream=pdf_path, filetype="pdf")
            elif isinstance(pdf_path, str) and os.path.isfile(pdf_path) and pdf_path.lower().endswith(".pdf"):
                doc = pymupdf.open(pdf_path)
            else:
                raise ValueError(f"Invalid PDF path: {pdf_path}")

            page_list = []
            for page in doc:
                page_text = page.get_text()
                token_length = len(enc.encode(page_text))
                page_list.append((page_text, token_length))
            doc.close()
            return page_list
        except Exception as e:
            raise ValueError(f"Error processing PDF with PyMuPDF: {e}")
    else:
        raise ValueError(f"Unsupported PDF parser: {pdf_parser}")


def get_text_of_pdf_pages(
    pdf_pages: List[Tuple[str, int]],
    start_page: int,
    end_page: int
) -> str:
    """
    Extract text from specific pages of pre-processed PDF pages.

    Args:
        pdf_pages: List of (page_text, token_count) tuples
        start_page: Starting page number (1-indexed)
        end_page: Ending page number (1-indexed, inclusive)

    Returns:
        Combined text from specified pages

    Raises:
        ValueError: If invalid page range
    """
    if not pdf_pages:
        return ""

    if start_page < 1 or end_page < start_page or end_page > len(pdf_pages):
        raise ValueError(f"Invalid page range: {start_page}-{end_page} for {len(pdf_pages)} pages")

    text = ""
    for page_num in range(start_page-1, end_page):
        text += pdf_pages[page_num][0]
    return text


def get_text_of_pdf_pages_with_labels(
    pdf_pages: List[Tuple[str, int]],
    start_page: int,
    end_page: int
) -> str:
    """
    Extract text from specific pages with physical index labels.

    Args:
        pdf_pages: List of (page_text, token_count) tuples
        start_page: Starting page number (1-indexed)
        end_page: Ending page number (1-indexed, inclusive)

    Returns:
        Combined text with physical index labels

    Raises:
        ValueError: If invalid page range
    """
    if not pdf_pages:
        return ""

    if start_page < 1 or end_page < start_page or end_page > len(pdf_pages):
        raise ValueError(f"Invalid page range: {start_page}-{end_page} for {len(pdf_pages)} pages")

    text = ""
    for page_num in range(start_page-1, end_page):
        text += f"<physical_index_{page_num+1}>\n{pdf_pages[page_num][0]}\n<physical_index_{page_num+1}>\n"
    return text


def get_number_of_pages(pdf_path: str) -> int:
    """
    Get the number of pages in a PDF file.

    Args:
        pdf_path: Path to the PDF file

    Returns:
        Number of pages in the PDF

    Raises:
        ValueError: If PyPDF2 is not available or file doesn't exist
    """
    if PyPDF2 is None:
        raise ValueError("PyPDF2 is not installed. Please install it to count PDF pages.")

    if not os.path.exists(pdf_path):
        raise FileNotFoundError(f"PDF file not found: {pdf_path}")

    try:
        pdf_reader = PyPDF2.PdfReader(pdf_path)
        return len(pdf_reader.pages)
    except Exception as e:
        raise ValueError(f"Error reading PDF {pdf_path}: {e}")



def post_processing(
    structure: List[Dict[str, Any]],
    end_physical_index: int
) -> Union[List[Dict[str, Any]], List[Dict[str, Any]]]:
    """
    Post-process structure data by converting to tree format.

    Args:
        structure: List of structure items
        end_physical_index: The final physical index

    Returns:
        Tree structure or cleaned flat structure
    """
    if not structure or not isinstance(structure, list):
        return []

    # First convert page_number to start_index in flat list
    for i, item in enumerate(structure):
        item['start_index'] = item.get('physical_index')
        if i < len(structure) - 1:
            next_item = structure[i + 1]
            if next_item.get('appear_start') == 'yes':
                item['end_index'] = next_item['physical_index'] - 1
            else:
                item['end_index'] = next_item['physical_index']
        else:
            item['end_index'] = end_physical_index

    tree = list_to_tree(structure)
    if len(tree) != 0:
        return tree
    else:
        # Remove appear_start and physical_index if tree conversion failed
        for node in structure:
            node.pop('appear_start', None)
            node.pop('physical_index', None)
        return structure


def clean_structure_post(data: Union[Dict[str, Any], List[Any]]) -> Union[Dict[str, Any], List[Any]]:
    """
    Remove page-related fields from structure data.

    Args:
        data: Structure data to clean

    Returns:
        Cleaned structure data
    """
    if isinstance(data, dict):
        data.pop('page_number', None)
        data.pop('start_index', None)
        data.pop('end_index', None)
        if 'nodes' in data:
            clean_structure_post(data['nodes'])
    elif isinstance(data, list):
        for section in data:
            clean_structure_post(section)
    return data


def remove_structure_text(data: Union[Dict[str, Any], List[Any]]) -> Union[Dict[str, Any], List[Any]]:
    """
    Remove text fields from structure data.

    Args:
        data: Structure data to clean

    Returns:
        Structure data without text fields
    """
    if isinstance(data, dict):
        data.pop('text', None)
        if 'nodes' in data:
            remove_structure_text(data['nodes'])
    elif isinstance(data, list):
        for item in data:
            remove_structure_text(item)
    return data


def check_token_limit(structure: Union[Dict[str, Any], List[Any]], limit: int = 110000) -> None:
    """
    Check if any nodes in the structure exceed the token limit.

    Args:
        structure: Structure to check
        limit: Token limit threshold
    """
    node_list = structure_to_list(structure)
    for node in node_list:
        if 'text' not in node:
            continue
        try:
            num_tokens = count_tokens(node['text'], model='gpt-4o')
            if num_tokens > limit:
                print(f"Node ID: {node.get('node_id', 'unknown')} has {num_tokens} tokens")
                print("Start Index:", node.get('start_index', 'unknown'))
                print("End Index:", node.get('end_index', 'unknown'))
                print("Title:", node.get('title', 'unknown'))
                print("\n")
        except Exception as e:
            logging.error(f"Error checking token limit for node {node.get('node_id', 'unknown')}: {e}")


def convert_physical_index_to_int(data: Union[List[Dict[str, Any]], str]) -> Union[List[Dict[str, Any]], int, None]:
    """
    Convert physical index strings to integers.

    Args:
        data: Data containing physical index information

    Returns:
        Data with converted physical indices
    """
    if isinstance(data, list):
        for i in range(len(data)):
            # Check if item is a dictionary and has 'physical_index' key
            if isinstance(data[i], dict) and 'physical_index' in data[i]:
                if isinstance(data[i]['physical_index'], str):
                    try:
                        if data[i]['physical_index'].startswith('<physical_index_'):
                            data[i]['physical_index'] = int(data[i]['physical_index'].split('_')[-1].rstrip('>').strip())
                        elif data[i]['physical_index'].startswith('physical_index_'):
                            data[i]['physical_index'] = int(data[i]['physical_index'].split('_')[-1].strip())
                    except (ValueError, IndexError) as e:
                        logging.error(f"Error converting physical index: {e}")
                        # Keep original value if conversion fails
    elif isinstance(data, str):
        try:
            if data.startswith('<physical_index_'):
                return int(data.split('_')[-1].rstrip('>').strip())
            elif data.startswith('physical_index_'):
                return int(data.split('_')[-1].strip())
        except (ValueError, IndexError) as e:
            logging.error(f"Error converting physical index string: {e}")
            return None
    return data


def convert_page_to_int(data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Convert page strings to integers in a list of dictionaries.

    Args:
        data: List of dictionaries that may contain 'page' fields

    Returns:
        Data with converted page numbers
    """
    if not isinstance(data, list):
        return data

    for item in data:
        if isinstance(item, dict) and 'page' in item and isinstance(item['page'], str):
            try:
                item['page'] = int(item['page'])
            except ValueError:
                # Keep original value if conversion fails
                logging.warning(f"Could not convert page '{item['page']}' to integer")
    return data


def add_node_text(node: Union[Dict[str, Any], List[Any]], pdf_pages: List[Tuple[str, int]]) -> None:
    """
    Add text content to nodes from PDF pages.

    Args:
        node: Node or list of nodes to add text to
        pdf_pages: List of (page_text, token_count) tuples
    """
    if isinstance(node, dict):
        start_page = node.get('start_index')
        end_page = node.get('end_index')
        if start_page is not None and end_page is not None:
            try:
                node['text'] = get_text_of_pdf_pages(pdf_pages, start_page, end_page)
            except Exception as e:
                logging.error(f"Error adding text to node: {e}")
                node['text'] = ""
        if 'nodes' in node:
            add_node_text(node['nodes'], pdf_pages)
    elif isinstance(node, list):
        for item in node:
            add_node_text(item, pdf_pages)


def add_node_text_with_labels(node: Union[Dict[str, Any], List[Any]], pdf_pages: List[Tuple[str, int]]) -> None:
    """
    Add text content with labels to nodes from PDF pages.

    Args:
        node: Node or list of nodes to add text to
        pdf_pages: List of (page_text, token_count) tuples
    """
    if isinstance(node, dict):
        start_page = node.get('start_index')
        end_page = node.get('end_index')
        if start_page is not None and end_page is not None:
            try:
                node['text'] = get_text_of_pdf_pages_with_labels(pdf_pages, start_page, end_page)
            except Exception as e:
                logging.error(f"Error adding labeled text to node: {e}")
                node['text'] = ""
        if 'nodes' in node:
            add_node_text_with_labels(node['nodes'], pdf_pages)
    elif isinstance(node, list):
        for item in node:
            add_node_text_with_labels(item, pdf_pages)


async def generate_node_summary(node: Dict[str, Any], model: Optional[str] = None) -> str:
    """
    Generate a summary for a document node.

    Args:
        node: Node containing text to summarize
        model: Model to use for generation

    Returns:
        Generated summary text
    """
    if 'text' not in node or not node['text']:
        return "No content available for summary."

    prompt = f"""You are given a part of a document, your task is to generate a description of the partial document about what are main points covered in the partial document.

    Partial Document Text: {node['text']}

    Directly return the description, do not include any other text.
    """
    try:
        response = await ChatGPT_API_async(model, prompt)
        return response
    except Exception as e:
        logging.error(f"Error generating node summary: {e}")
        return "Error generating summary."


async def generate_summaries_for_structure(
    structure: Union[Dict[str, Any], List[Any]],
    model: Optional[str] = None
) -> Union[Dict[str, Any], List[Any]]:
    """
    Generate summaries for all nodes in a structure.

    Args:
        structure: Document structure to generate summaries for
        model: Model to use for generation

    Returns:
        Structure with added summaries
    """
    nodes = structure_to_list(structure)
    tasks = [generate_node_summary(node, model=model) for node in nodes if 'text' in node]

    try:
        summaries = await asyncio.gather(*tasks)

        # Add summaries to nodes that have text
        summary_index = 0
        for node in nodes:
            if 'text' in node:
                node['summary'] = summaries[summary_index]
                summary_index += 1
    except Exception as e:
        logging.error(f"Error generating summaries for structure: {e}")

    return structure


def generate_doc_description(structure: Union[Dict[str, Any], List[Any]], model: Optional[str] = None) -> str:
    """
    Generate a description for a document based on its structure.

    Args:
        structure: Document structure
        model: Model to use for generation

    Returns:
        Generated document description
    """
    prompt = f"""You are an expert in generating descriptions for a document.
    You are given a structure of a document. Your task is to generate a one-sentence description for the document, which makes it easy to distinguish the document from other documents.

    Document Structure: {structure}

    Directly return the description, do not include any other text.
    """
    try:
        response = ChatGPT_API(model, prompt)
        return response
    except Exception as e:
        logging.error(f"Error generating document description: {e}")
        return "Error generating document description."


class ConfigLoader:
    """
    Configuration loader that merges default YAML config with user options.
    """

    def __init__(self, default_path: Optional[str] = None):
        """
        Initialize the config loader.

        Args:
            default_path: Path to default config YAML file
        """
        if default_path is None:
            default_path = Path(__file__).parent / "config.yaml"

        try:
            self._default_dict = self._load_yaml(default_path)
        except Exception as e:
            logging.warning(f"Could not load default config from {default_path}: {e}")
            self._default_dict = {}

    @staticmethod
    def _load_yaml(path: Union[str, Path]) -> Dict[str, Any]:
        """
        Load YAML configuration file.

        Args:
            path: Path to YAML file

        Returns:
            Loaded configuration dictionary

        Raises:
            FileNotFoundError: If config file doesn't exist
            yaml.YAMLError: If YAML parsing fails
        """
        path = Path(path)
        if not path.exists():
            raise FileNotFoundError(f"Config file not found: {path}")

        with open(path, "r", encoding="utf-8") as f:
            return yaml.safe_load(f) or {}

    def _validate_keys(self, user_dict: Dict[str, Any]) -> None:
        """
        Validate that user dictionary keys are known.

        Args:
            user_dict: User configuration dictionary

        Raises:
            ValueError: If unknown keys are found
        """
        if not self._default_dict:
            # If no default config, allow any keys
            return

        unknown_keys = set(user_dict) - set(self._default_dict)
        if unknown_keys:
            raise ValueError(f"Unknown config keys: {unknown_keys}")

    def load(self, user_opt: Optional[Union[Dict[str, Any], config]] = None) -> config:
        """
        Load the configuration, merging user options with default values.

        Args:
            user_opt: User configuration options

        Returns:
            Merged configuration as SimpleNamespace

        Raises:
            TypeError: If user_opt is not a valid type
            ValueError: If unknown config keys are provided
        """
        if user_opt is None:
            user_dict = {}
        elif isinstance(user_opt, config):
            user_dict = vars(user_opt)
        elif isinstance(user_opt, dict):
            user_dict = user_opt
        else:
            raise TypeError("user_opt must be dict, config(SimpleNamespace) or None")

        self._validate_keys(user_dict)
        merged = {**self._default_dict, **user_dict}
        return config(**merged)