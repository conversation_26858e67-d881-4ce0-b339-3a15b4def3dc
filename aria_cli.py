import argparse
import json
import sys
import os
from aria.project_manager import ProjectManager
from aria.agent.core import aria_app
from aria.preprocess.preproc_utils import process_document_to_markdown
from aria.rules.rule_checker import <PERSON>ume<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RuleResult

def main():
    parser = argparse.ArgumentParser(description="ARIA Unified Command-Line Interface.")
    subparsers = parser.add_subparsers(dest="command_group", help="Top-level commands", required=True)

    # --- Project Command Group ---
    project_parser = subparsers.add_parser("project", help="Manage ARIA projects.")
    project_subparsers = project_parser.add_subparsers(dest="project_command", help="Project management actions", required=True)

    # Project -> add_preprocessed
    parser_add_preprocessed = project_subparsers.add_parser("add_preprocessed", help="Add a new, already preprocessed project.")
    parser_add_preprocessed.add_argument("name", type=str, help="Name of the project.")
    parser_add_preprocessed.add_argument("path", type=str, help="Absolute or relative path to the preprocessed project data directory.")

    # Project -> add (Placeholder)
    parser_add_full = project_subparsers.add_parser("add", help="Add a new project and initiate preprocessing (Not yet implemented).")
    parser_add_full.add_argument("name", type=str, help="Name of the project.")
    parser_add_full.add_argument("raw_data_path", type=str, help="Path to the raw project data directory.")

    # Project -> list
    project_subparsers.add_parser("list", help="List all registered projects.")

    # Project -> remove
    parser_remove = project_subparsers.add_parser("remove", help="Remove a project from the registry.")
    parser_remove.add_argument("name", type=str, help="Name of the project to remove.")

    # Project -> get
    parser_get = project_subparsers.add_parser("get", help="Get the path of a specific project.")
    parser_get.add_argument("name", type=str, help="Name of the project to retrieve.")

    # --- Query Command Group ---
    query_parser = subparsers.add_parser("query", help="Run a query against an ARIA project.")
    query_parser.add_argument("--project_name", type=str, required=True, help="Name of the project to query.")
    query_parser.add_argument("--query_string", type=str, required=True, help="Natural language query string.") # Renamed from --query to avoid conflict

    # --- Rules Command Group ---
    rules_parser = subparsers.add_parser("rules", help="Rule checking and validation.")
    rules_subparsers = rules_parser.add_subparsers(dest="rules_command", help="Rule actions", required=True)

    # Rules -> check
    parser_rules_check = rules_subparsers.add_parser("check", help="Run rule checks on a project/group.")
    parser_rules_check.add_argument("--group_id", type=str, required=True, help="Graphiti group/namespace to check.")
    parser_rules_check.add_argument("--rules_config", type=str, default="./data/queries/rules_config.yaml", help="Path to rules config YAML file.")

    args = parser.parse_args()
    
    project_manager = ProjectManager() # Uses default "aria_projects.json"

    if args.command_group == "project":
        if args.project_command == "add_preprocessed":
            project_manager.add_project(args.name, args.path)
            # Message printed by ProjectManager
        elif args.project_command == "add":
            project_name = args.name
            raw_data_path = os.path.abspath(args.raw_data_path)

            if not os.path.isdir(raw_data_path):
                print(f"Error: Raw data path '{raw_data_path}' does not exist or is not a directory.")
                sys.exit(1)
            print("[Placeholder] Project add and preprocessing not yet implemented.")

    elif args.command_group == "rules":
        if args.rules_command == "check":
            import asyncio
            from dotenv import load_dotenv
            from aria.rules.rule_checker import DocumentRuleChecker, RuleResult
            from graphiti_core import Graphiti
            import os

            load_dotenv()
            neo4j_uri = os.getenv('NEO4J_URI', 'bolt://localhost:7687')
            neo4j_user = os.getenv('NEO4J_USER', 'neo4j')
            neo4j_password = os.getenv('NEO4J_PASSWORD', 'password')
            graphiti = Graphiti(neo4j_uri, neo4j_user, neo4j_password)
            print("🔌 Connecting to Graphiti database...")

            async def run_rule_check():
                checker = DocumentRuleChecker(graphiti, args.group_id)
                checker.load_rules_from_file(args.rules_config)
                results = await checker.check_rules()
                report = checker.generate_report(results)
                print(report)
                await graphiti.close()

            asyncio.run(run_rule_check())


            preprocessed_base_dir = os.path.abspath("data/preprocessed")
            project_output_dir = os.path.join(preprocessed_base_dir, project_name)
            reports_output_dir = os.path.join(project_output_dir, "reports")
            
            try:
                os.makedirs(reports_output_dir, exist_ok=True)
                print(f"[INFO] Ensured output directory exists: {reports_output_dir}")
            except OSError as e:
                print(f"[ERROR] Could not create output directories: {e}")
                sys.exit(1)

            processed_doc_ids = []
            print(f"[INFO] Starting processing of files from '{raw_data_path}'...")
            for filename in os.listdir(raw_data_path):
                full_input_file_path = os.path.join(raw_data_path, filename)
                if os.path.isfile(full_input_file_path):
                    print(f"[INFO] Processing file: {filename}")
                    output_md_path = process_document_to_markdown(full_input_file_path, reports_output_dir)
                    if output_md_path:
                        doc_id = os.path.splitext(os.path.basename(output_md_path))[0]
                        processed_doc_ids.append(doc_id)
                    else:
                        print(f"[WARN] Failed to process file: {filename}")
                else:
                    print(f"[INFO] Skipping non-file item: {filename}")
            
            if processed_doc_ids:
                index_data = {
                    "documents": [
                        {
                            "id": doc_id,
                            "title": doc_id.replace("_", " ").replace("-", " ").title(), # Simple title
                            "sections": [] # Placeholder
                        }
                        for doc_id in processed_doc_ids
                    ]
                }
                index_json_path = os.path.join(project_output_dir, "index.json")
                try:
                    with open(index_json_path, 'w') as f:
                        json.dump(index_data, f, indent=4)
                    print(f"[INFO] Successfully created index.json at {index_json_path}")
                    
                    # Register project
                    project_manager.add_project(project_name, project_output_dir)
                    # Message printed by ProjectManager
                    print(f"[INFO] Project '{project_name}' successfully processed and registered.")

                except Exception as e:
                    print(f"[ERROR] Failed to write index.json or register project: {e}")
                    sys.exit(1) # Exit if index creation or registration fails
            else:
                print("[WARN] No documents were successfully processed. index.json not created, and project not registered.")
                # Attempt to clean up created directories
                try:
                    if os.path.exists(reports_output_dir) and not os.listdir(reports_output_dir):
                        os.rmdir(reports_output_dir)
                        print(f"[INFO] Cleaned up empty reports directory: {reports_output_dir}")
                    if os.path.exists(project_output_dir) and not os.listdir(project_output_dir):
                        os.rmdir(project_output_dir)
                        print(f"[INFO] Cleaned up empty project directory: {project_output_dir}")
                    # Optionally, attempt to clean up preprocessed_base_dir if it's now empty
                    # This should be done carefully, ensuring it only removes it if it was created by this process
                    # and is truly empty, to avoid unintended data loss.
                    # For now, the focus is on project_output_dir and reports_output_dir.
                    # if os.path.exists(preprocessed_base_dir) and not os.listdir(preprocessed_base_dir):
                    #     # Check if preprocessed_base_dir is still the parent of project_output_dir
                    #     # This is a simple check; more robust checks might be needed if paths can be arbitrary
                    #     if os.path.abspath(os.path.join(project_output_dir, "..")) == preprocessed_base_dir:
                    #         os.rmdir(preprocessed_base_dir)
                    #         print(f"[INFO] Cleaned up empty base preprocessed directory: {preprocessed_base_dir}")
                except OSError as e:
                    print(f"[WARN] Could not fully clean up empty directories: {e}")

        elif args.project_command == "list":
            projects = project_manager.list_projects()
            if projects:
                print("Registered projects:")
                print(json.dumps(projects, indent=2))
            else:
                print("No projects are currently registered.")
        elif args.project_command == "remove":
            project_manager.remove_project(args.name)
            # Message printed by ProjectManager
        elif args.project_command == "get":
            path = project_manager.get_project_path(args.name)
            if path:
                print(f"Project: {args.name}")
                print(f"Path: {path}")
            else:
                print(f"Project '{args.name}' not found in configuration.")

    elif args.command_group == "query":
        project_path = project_manager.get_project_path(args.project_name)

        if project_path is None:
            print(f"Error: Project '{args.project_name}' not found in configuration. Please add it using 'aria_cli.py project add_preprocessed ...'.")
            sys.exit(1)
        
        
        # Use the method from ProjectManager instance
        if not project_manager.is_path_valid_project_directory(project_path):
            print(f"Error: The path '{project_path}' for project '{args.project_name}' is not a valid project directory. Check for 'index.json' and 'reports/' subdirectory.")
            sys.exit(1) # Exit if project path is not valid

        initial_state = {
            "query": args.query_string,
            "project_path": project_path,
            "documents_to_explore": [],
            "explored_documents": [],
        }

        print(f"Invoking ARIA with query: '{args.query_string}' on project: '{args.project_name}' (Path: '{project_path}')")
        try:
            result = aria_app.invoke(initial_state)
            final_answer = result.get("final_answer", {"error": "No answer found or error in processing."})
            print("\nFinal Answer:")
            print(json.dumps(final_answer, indent=2))
        except Exception as e:
            print(f"\nAn error occurred during ARIA agent execution:")
            print(f"Error type: {type(e).__name__}")
            print(f"Error message: {e}")
            # Potentially print traceback if in debug mode
            # import traceback
            # traceback.print_exc()
            sys.exit(1)

if __name__ == "__main__":
    main()
