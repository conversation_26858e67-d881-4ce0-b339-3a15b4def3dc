// Updated BAML classes for query classification

class QueryEntity {
    name        string     @description("entity name, e.g. \"drug\", \"patient\", \"study\"")
    type        string     @description("entity type, e.g. \"drug\", \"person\", \"measurement\", \"condition\"")
    attributes  string[]   @description("relevant attributes or qualifiers, e.g. [\"male\", \"rat\"] for \"male rats\"")
}

class SearchStrategy {
    primary_target   string    @description("what to search for: \"nodes\" | \"edges\" | \"communities\" | \"paths\" | \"aggregation\"")
    approach        string    @description("search approach: \"semantic\" | \"graph_distance\" | \"temporal\" | \"comparative\" | \"comprehensive\"")
    reranking       string?   @description("reranking method: \"rrf\" | \"mmr\" | \"cross_encoder\" | \"node_distance\" | \"episode_mentions\" | null")
    explanation     string    @description("brief explanation of why this strategy fits the query")
}

class QueryClassification {
    category        string        @description("one of: \"entity_identification\" | \"fact_relationship\" | \"contextual_proximity\" | \"path_multihop\" | \"aggregation_meta\" | \"temporal\" | \"comparative\" | \"exploratory\" | \"verification\"")
    confidence      float         @description("confidence score between 0.0 and 1.0")
    reasoning       string[]      @description("list of specific reasons for this classification")
    entities        QueryEntity[] @description("key entities identified in the query")
    search_strategy SearchStrategy @description("recommended search strategy for this query type")
    query_intent    string        @description("natural language description of what the user is trying to find")
    original_query  string        @description("verbatim copy of user query")
}

function QueryClassifier(query: string) -> QueryClassification {
    client Fast
    prompt #"
        _.role(system)
You are **ARIA-Classify v2.0**, an expert query classification system for knowledge graph search.

Your job is to analyze natural language queries and classify them into one of 9 categories, each optimized for different search strategies:

**CATEGORIES & SEARCH STRATEGIES:**

1. **entity_identification** - "What is X?" "Who is Y?" "Find the drug"
   - Target: nodes (entities)
   - Approach: semantic search for entity names and descriptions
   - Best for: Finding specific people, drugs, compounds, patients, researchers

2. **fact_relationship** - "X causes Y" "How does Z work?" "What effects does A have?"
   - Target: edges (facts/relationships) 
   - Approach: semantic search for relationship statements
   - Best for: Finding specific claims, causal relationships, mechanisms

3. **contextual_proximity** - "Everything about X" "All information on Y" "Tell me about Z"
   - Target: edges reranked by graph distance
   - Approach: find central entity, then rank facts by proximity
   - Best for: Comprehensive information around a specific entity

4. **path_multihop** - "How is X connected to Y?" "Path from A to B" "Chain of events"
   - Target: paths between entities
   - Approach: graph traversal and path finding
   - Best for: Multi-hop relationships, causal chains

5. **aggregation_meta** - "How many X?" "List all Y" "Count of Z" "Total number"
   - Target: aggregation of nodes/communities
   - Approach: comprehensive search with counting/grouping
   - Best for: Quantitative queries, lists, counts, statistics

6. **temporal** - "When did X happen?" "Before/after Y" "Timeline of Z"
   - Target: edges with temporal filtering
   - Approach: temporal search with episode context
   - Best for: Time-based queries, chronology, sequences

7. **comparative** - "X vs Y" "Compare A and B" "Difference between X and Y"
   - Target: edges with diversity ranking
   - Approach: parallel search with diverse results
   - Best for: Comparisons, contrasts, similarities/differences

8. **exploratory** - "What's interesting?" "Show insights" "Discover patterns"
   - Target: comprehensive search across all types
   - Approach: broad search for discovery
   - Best for: Open-ended exploration, pattern discovery

9. **verification** - "Is X true?" "Does Y cause Z?" "Evidence for X"
   - Target: edges with confidence scoring
   - Approach: cross-encoder for relevance scoring
   - Best for: Truth verification, evidence gathering, confirmation

**OUTPUT FORMAT:**
Return a JSON object that conforms **exactly** to the QueryClassification schema. No additional keys, no comments, no markdown formatting.

**CLASSIFICATION RULES:**
- Choose the MOST SPECIFIC category that fits
- Confidence should reflect how clearly the query fits the category (0.0-1.0)
- Provide 2-4 specific reasoning points
- Identify key entities with their types and attributes
- Recommend the optimal search strategy
- If uncertain between categories, prefer the more specific one

**OUTPUT FORMAT:**
{{ ctx.output_format }}
        _.role(user)

Classify this query:
{{ query }}

    "#
}

// Alternative function for batch classification
function BatchQueryClassifier(queries: string[]) -> QueryClassification[] {
    client Fast
    prompt #"
        _.role(system)
You are **ARIA-Classify v2.0**, classifying multiple queries at once.

Use the same 9-category system and search strategies as the single query classifier.

Return a JSON array of QueryClassification objects, one for each input query in the same order.

Output format:

{{ ctx.output_format }}

        _.role(user)

Classify these queries:
{% for query in queries %}
{{ loop.index }}. {{ query }}
{% endfor %}

    "#
}

// Function to explain classification decisions in detail
function ExplainQueryClassification(query: string, category: string) -> string {
    client Fast
    prompt #"
        _.role(system)
You are **ARIA-Explain v2.0**, providing detailed explanations for query classifications.

Given a query and its assigned category, explain in detail:
1. Why this category was chosen
2. What linguistic patterns led to this decision
3. What search strategy would be optimal and why
4. Any alternative categories that were considered

Be educational and thorough in your explanation.

{{ ctx.output_format }}
        _.role(user)

Query: {{ query }}
Assigned Category: {{ category }}

Provide a detailed explanation of this classification decision.

    "#
}

// Test cases for the classifier
test TestEntityIdentification {
    functions [QueryClassifier]
    args {
        query "What is the drug of interest?"
    }
}

test TestFactRelationship {
    functions [QueryClassifier]
    args {
        query "What side effects does the drug cause?"
    }
}

test TestContextualProximity {
    functions [QueryClassifier]
    args {
        query "Tell me everything about the liver toxicity study"
    }
}

test TestAggregationMeta {
    functions [QueryClassifier]
    args {
        query "How many patients experienced adverse events?"
    }
}

test TestComparative {
    functions [QueryClassifier]
    args {
        query "Compare the side effects in male vs female rats"
    }
}

test TestTemporal {
    functions [QueryClassifier]
    args {
        query "When were the first symptoms observed?"
    }
}

test TestVerification {
    functions [QueryClassifier]
    args {
        query "Is there evidence that the drug causes liver damage?"
    }
}

test TestExploratory {
    functions [QueryClassifier]
    args {
        query "What interesting patterns emerge from the study data?"
    }
}

test TestBatchClassification {
    functions [BatchQueryClassifier]
    args {
        queries [
            "What is the primary endpoint?",
            "How many subjects completed the study?",
            "Compare treatment vs control groups"
        ]
    }
}

test TestExplainQueryClassification {
    functions [ExplainQueryClassification]
    args {
        query "What is the drug of interest?"
        category "entity_identification"
    }
}