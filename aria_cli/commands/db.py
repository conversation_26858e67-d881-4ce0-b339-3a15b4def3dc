import typer
from rich.console import Console
from rich.table import Table
import asyncio

from aria.memory.graphiti_admin_manager import GraphitiAdminManager
from aria.db_service import get_graphiti_client
from aria.memory.graphiti_runtime_manager import GraphitiRuntimeManager


db_app = typer.Typer()

@db_app.command("stats")
def db_stats():
    """Show Graphiti database statistics."""
    console = Console()

    async def fetch_stats():
        client = get_graphiti_client()
        manager = GraphitiAdminManager(client)
        stats = await manager.get_database_statistics(verbose=False)
        return stats

    stats = asyncio.run(fetch_stats())

    if "error" in stats:
        console.print(f"[red]Error fetching database stats: {stats['error']}[/red]")
        raise typer.Exit(1)

    table = Table(title="Graphiti Database Statistics")
    table.add_column("Metric", style="cyan", no_wrap=True)
    table.add_column("Value", style="magenta")

    table.add_row("Timestamp", stats.get("timestamp", "-"))
    table.add_row("Total Nodes", str(stats.get("total_nodes", "-")))
    table.add_row("Total Relationships", str(stats.get("total_relationships", "-")))
    table.add_row("Total Groups", str(stats.get("total_groups", "-")))
    table.add_row("Database Size (MB)", str(stats.get("database_size_mb", "-")))

    console.print(table)

    # Optionally show group IDs, node labels, relationship types
    if stats.get("group_ids"):
        console.print("\n[bold]Group IDs:[/bold] " + ", ".join(stats["group_ids"]))
    if stats.get("node_labels"):
        console.print("\n[bold]Node Labels:[/bold]")
        for label, count in stats["node_labels"].items():
            console.print(f"  [green]{label}[/green]: {count}")
    if stats.get("relationship_types"):
        console.print("\n[bold]Relationship Types:[/bold]")
        for rel_type, count in stats["relationship_types"].items():
            console.print(f"  [yellow]{rel_type}[/yellow]: {count}")

from rich.progress import Progress, SpinnerColumn, TextColumn

@db_app.command("rebuild-communities")
def rebuild_communities(group_ids: list[str]| None=None):
    """Manually (re-)build all communities in the Graphiti DB."""
    console = Console()
    async def do_rebuild():
        client = get_graphiti_client()
        manager = GraphitiRuntimeManager(client)
        console.print(f"[bold blue]Starting community rebuild for group_ids: {group_ids if group_ids else '[ALL GROUPS]'}[/bold blue]")
        with Progress(SpinnerColumn(), TextColumn("{task.description}"), transient=True) as progress:
            task = progress.add_task("Rebuilding communities...", start=True)
            await manager.rebuild_communities(group_ids)
            progress.update(task, description="Finishing up...")
        console.print("[bold green]Community rebuild finished![/bold green]")
        return True
    try:
        console.print("[cyan]Initializing rebuild process...[/cyan]")
        asyncio.run(do_rebuild())
        console.print("[green]Successfully rebuilt all communities in the database.[/green]")
    except Exception as e:
        console.print(f"[red]Failed to rebuild communities: {e}[/red]")
        raise typer.Exit(1)
