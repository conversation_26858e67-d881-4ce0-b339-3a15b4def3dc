"""
ARIA CLI - Modern command-line interface for <PERSON> (Automated Research Intelligence Assistant)
"""
import typer
from rich.console import Console
from rich.traceback import install
from typing import Optional
import sys

from .config import load_config, get_config
from .utils.log_utils import setup_logging
from .exceptions import ARIAError
from .commands import project_app, query_app, rules_app, db_app
from .commands.config import config_app
from .commands.doctor import doctor_app

# Install rich tracebacks
install(show_locals=True)

app = typer.Typer(
    name="aria",
    help="🤖 ARIA - Automated Research Intelligence Assistant",
    rich_markup_mode="rich"
)
console = Console()

# Add command groups
app.add_typer(project_app, name="project")
app.add_typer(query_app, name="query")
app.add_typer(rules_app, name="rules")
app.add_typer(db_app, name="db")
app.add_typer(config_app, name="config")
app.add_typer(doctor_app, name="doctor")

@app.callback()
def main(
    verbose: bool = typer.Option(False, "--verbose", "-v", help="Enable verbose logging"),
    quiet: bool = typer.Option(False, "--quiet", "-q", help="Suppress non-essential output"),
    config_file: Optional[str] = typer.Option(None, "--config", help="Custom config file path"),
    version: bool = typer.Option(False, "--version", help="Show version and exit")
):
    """🤖 ARIA - Automated Research Intelligence Assistant"""

    if version:
        console.print("ARIA CLI v2.0.0")
        raise typer.Exit()

    # Setup logging and config
    setup_logging(verbose, quiet)
    load_config(config_file)

def handle_exceptions():
    """Main entry point with exception handling"""
    try:
        app()
    except ARIAError as e:
        console.print(f"[red]❌ {e}[/red]")
        sys.exit(e.exit_code)
    except KeyboardInterrupt:
        console.print("\n[yellow]⚠️ Operation cancelled by user[/yellow]")
        sys.exit(130)  # Standard exit code for SIGINT
    except Exception as e:
        console.print(f"[red]💥 Unexpected error: {e}[/red]")
        if get_config().log_level == "DEBUG":
            console.print_exception()
        sys.exit(99)

@app.command()
def completion(
    shell: str = typer.Argument(..., help="Shell type (bash, zsh, fish, powershell)")
):
    """Generate shell completion scripts"""
    completion_scripts = {
        "bash": "_ARIA_COMPLETE=bash_complete aria",
        "zsh": "_ARIA_COMPLETE=zsh_complete aria",
        "fish": "_ARIA_COMPLETE=fish_complete aria",
        "powershell": "_ARIA_COMPLETE=powershell_complete aria"
    }
    if shell not in completion_scripts:
        console.print(f"[red]Unsupported shell: {shell}[/red]")
        console.print(f"Supported shells: {', '.join(completion_scripts.keys())}")
        raise typer.Exit(1)
    console.print(f"# Add this to your {shell} configuration:")
    console.print(f"eval \"$({completion_scripts[shell]})\"")

if __name__ == "__main__":
    sys.exit(app(prog_name="aria"))
    handle_exceptions()
