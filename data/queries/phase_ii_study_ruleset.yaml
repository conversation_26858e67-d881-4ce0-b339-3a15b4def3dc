# Phase II Randomized, Double-Blind, Placebo-Controlled Study
# Compliance Rules Configuration
# Based on FDA Guidance, ICH-GCP, and Clinical Research Standards

rules:
  # ========================================
  # REGULATORY COMPLIANCE RULES
  # ========================================
  
  - id: "REG_001"
    name: "IRB/Ethics Committee Approval"
    description: "Study must document IRB or Ethics Committee approval"
    category: "regulatory"
    severity: "critical"
    type: "content_pattern"
    parameters:
      search_terms: ["IRB", "institutional review board", "ethics committee", "ethics board"]
      required_patterns: ["IRB.*approv", "ethics.*committee.*approv", "institutional.*review.*board"]
      minimum_pattern_matches: 1
    validation:
      confidence_threshold: 0.9

  - id: "REG_002"
    name: "Informed Consent Documentation"
    description: "Informed consent process must be thoroughly documented"
    category: "regulatory"
    severity: "critical"
    type: "content_pattern"
    parameters:
      search_terms: ["informed consent", "consent form", "patient consent", "subject consent"]
      required_patterns: ["informed.*consent", "consent.*form", "consent.*process"]
      minimum_pattern_matches: 2
    validation:
      confidence_threshold: 0.85

  - id: "REG_003"
    name: "Protocol Registration"
    description: "Clinical trial registration number must be documented"
    category: "regulatory"
    severity: "critical"
    type: "content_pattern"
    parameters:
      search_terms: ["clinicaltrials.gov", "clinical trial registration", "NCT", "protocol number"]
      patterns: ["NCT\\d{8}", "clinicaltrials\\.gov", "trial.*registration"]
      minimum_pattern_matches: 1
    validation:
      confidence_threshold: 0.8

  - id: "REG_004"
    name: "GCP Compliance Statement"
    description: "Good Clinical Practice compliance must be stated"
    category: "regulatory"
    severity: "critical"
    type: "content_pattern"
    parameters:
      search_terms: ["Good Clinical Practice", "GCP", "ICH-GCP", "clinical practice guidelines"]
      required_patterns: ["Good.*Clinical.*Practice", "GCP", "ICH.*GCP"]
      minimum_pattern_matches: 1
    validation:
      confidence_threshold: 0.8

  # ========================================
  # STUDY DESIGN REQUIREMENTS
  # ========================================

  - id: "DESIGN_001"
    name: "Primary Endpoint Definition"
    description: "Primary endpoint must be clearly defined and measurable"
    category: "study_design"
    severity: "critical"
    type: "content_pattern"
    parameters:
      search_terms: ["primary endpoint", "primary outcome", "primary efficacy"]
      required_patterns: ["primary.*endpoint", "primary.*outcome", "primary.*efficacy.*measure"]
      minimum_pattern_matches: 1
    validation:
      confidence_threshold: 0.85

  - id: "DESIGN_002"
    name: "Randomization Methodology"
    description: "Randomization method and procedures must be documented"
    category: "study_design"
    severity: "critical"
    type: "content_pattern"
    parameters:
      search_terms: ["randomization", "randomised", "randomized", "allocation", "block randomization"]
      required_patterns: ["random.*allocation", "randomization.*method", "block.*randomization"]
      minimum_pattern_matches: 1
    validation:
      confidence_threshold: 0.8

  - id: "DESIGN_003"
    name: "Blinding Procedures"
    description: "Double-blind procedures must be detailed"
    category: "study_design"
    severity: "critical"
    type: "content_pattern"
    parameters:
      search_terms: ["double-blind", "double blind", "blinding", "masked", "unblinding"]
      required_patterns: ["double.*blind", "blinding.*procedure", "masking", "unblinding.*procedure"]
      minimum_pattern_matches: 2
    validation:
      confidence_threshold: 0.8

  - id: "DESIGN_004"
    name: "Sample Size Justification"
    description: "Sample size calculation with power analysis must be provided"
    category: "study_design"
    severity: "critical"
    type: "relationship_exists"
    parameters:
      source_terms: ["sample size", "subjects", "patients", "participants"]
      target_terms: ["power analysis", "statistical power", "alpha", "beta", "effect size"]
      minimum_relationships: 1
    validation:
      confidence_threshold: 0.8

  - id: "DESIGN_005"
    name: "Inclusion Exclusion Criteria"
    description: "Clear inclusion and exclusion criteria must be specified"
    category: "study_design"
    severity: "critical"
    type: "content_pattern"
    parameters:
      search_terms: ["inclusion criteria", "exclusion criteria", "eligibility criteria"]
      required_patterns: ["inclusion.*criteria", "exclusion.*criteria", "eligibility.*criteria"]
      minimum_pattern_matches: 2
    validation:
      confidence_threshold: 0.9

  # ========================================
  # SAFETY MONITORING REQUIREMENTS
  # ========================================

  - id: "SAFETY_001"
    name: "Adverse Event Reporting"
    description: "Adverse event reporting procedures must be documented"
    category: "safety"
    severity: "critical"
    type: "content_pattern"
    parameters:
      search_terms: ["adverse event", "adverse reaction", "AE", "SAE", "serious adverse event"]
      required_patterns: ["adverse.*event", "adverse.*reaction", "\\bAE\\b", "\\bSAE\\b"]
      minimum_pattern_matches: 2
    validation:
      confidence_threshold: 0.85

  - id: "SAFETY_002"
    name: "Data Safety Monitoring Board"
    description: "Data Safety Monitoring Board or committee must be described"
    category: "safety"
    severity: "warning"
    type: "content_pattern"
    parameters:
      search_terms: ["Data Safety Monitoring Board", "DSMB", "safety monitoring committee", "DMC"]
      patterns: ["DSMB", "Data.*Safety.*Monitoring", "safety.*monitoring.*committee", "\\bDMC\\b"]
      minimum_pattern_matches: 1
    validation:
      confidence_threshold: 0.7

  - id: "SAFETY_003"
    name: "Stopping Rules"
    description: "Study stopping rules and criteria must be defined"
    category: "safety"
    severity: "warning"
    type: "content_pattern"
    parameters:
      search_terms: ["stopping rules", "termination criteria", "futility analysis", "interim analysis"]
      required_patterns: ["stopping.*rule", "termination.*criteria", "futility", "interim.*analysis"]
      minimum_pattern_matches: 1
    validation:
      confidence_threshold: 0.6

  # ========================================
  # STATISTICAL ANALYSIS REQUIREMENTS
  # ========================================

  - id: "STATS_001"
    name: "Statistical Analysis Plan"
    description: "Comprehensive statistical analysis plan must be provided"
    category: "statistics"
    severity: "critical"
    type: "content_pattern"
    parameters:
      search_terms: ["statistical analysis plan", "statistical methods", "analysis plan", "statistical approach"]
      required_patterns: ["statistical.*analysis.*plan", "statistical.*method", "analysis.*plan"]
      minimum_pattern_matches: 1
    validation:
      confidence_threshold: 0.8

  - id: "STATS_002"
    name: "Intent-to-Treat Analysis"
    description: "Intent-to-treat analysis approach must be specified"
    category: "statistics"
    severity: "critical"
    type: "content_pattern"
    parameters:
      search_terms: ["intent-to-treat", "intention-to-treat", "ITT", "per-protocol"]
      patterns: ["intent.*to.*treat", "intention.*to.*treat", "\\bITT\\b", "per.*protocol"]
      minimum_pattern_matches: 1
    validation:
      confidence_threshold: 0.8

  - id: "STATS_003"
    name: "Multiple Comparisons Adjustment"
    description: "Multiple comparisons correction must be addressed if applicable"
    category: "statistics"
    severity: "warning"
    type: "content_pattern"
    parameters:
      search_terms: ["multiple comparisons", "Bonferroni", "false discovery rate", "multiplicity"]
      patterns: ["multiple.*comparison", "Bonferroni", "false.*discovery", "multiplicity"]
      minimum_pattern_matches: 1
    validation:
      confidence_threshold: 0.6

  # ========================================
  # DATA QUALITY REQUIREMENTS
  # ========================================

  - id: "DATA_001"
    name: "Data Management Plan"
    description: "Data management procedures must be documented"
    category: "data_quality"
    severity: "critical"
    type: "content_pattern"
    parameters:
      search_terms: ["data management", "case report form", "CRF", "electronic data capture", "EDC"]
      required_patterns: ["data.*management", "case.*report.*form", "\\bCRF\\b", "electronic.*data"]
      minimum_pattern_matches: 2
    validation:
      confidence_threshold: 0.8

  - id: "DATA_002"
    name: "Quality Control Procedures"
    description: "Data quality control and validation procedures must be specified"
    category: "data_quality"
    severity: "critical"
    type: "content_pattern"
    parameters:
      search_terms: ["quality control", "data validation", "source data verification", "monitoring"]
      required_patterns: ["quality.*control", "data.*validation", "source.*data.*verification", "monitoring"]
      minimum_pattern_matches: 2
    validation:
      confidence_threshold: 0.8

  - id: "DATA_003"
    name: "Protocol Deviations Tracking"
    description: "Protocol deviations must be tracked and reported"
    category: "data_quality"
    severity: "warning"
    type: "content_pattern"
    parameters:
      search_terms: ["protocol deviation", "protocol violation", "major deviation", "minor deviation"]
      patterns: ["protocol.*deviation", "protocol.*violation", "major.*deviation"]
      minimum_pattern_matches: 1
    validation:
      confidence_threshold: 0.7

  # ========================================
  # RESULTS REPORTING REQUIREMENTS
  # ========================================

  - id: "RESULTS_001"
    name: "CONSORT Diagram"
    description: "Patient flow diagram following CONSORT guidelines"
    category: "reporting"
    severity: "warning"
    type: "content_pattern"
    parameters:
      search_terms: ["CONSORT", "patient flow", "participant flow", "enrollment", "allocation"]
      patterns: ["CONSORT", "patient.*flow", "participant.*flow", "flow.*diagram"]
      minimum_pattern_matches: 1
    validation:
      confidence_threshold: 0.6

  - id: "RESULTS_002"
    name: "Baseline Characteristics"
    description: "Baseline patient characteristics must be presented"
    category: "reporting"
    severity: "critical"
    type: "content_pattern"
    parameters:
      search_terms: ["baseline characteristics", "demographics", "baseline demographics", "patient characteristics"]
      required_patterns: ["baseline.*characteristic", "demographic", "baseline.*demographic"]
      minimum_pattern_matches: 1
    validation:
      confidence_threshold: 0.8

  - id: "RESULTS_003"
    name: "Confidence Intervals"
    description: "Results must include confidence intervals for key outcomes"
    category: "reporting"
    severity: "critical"
    type: "content_pattern"
    parameters:
      search_terms: ["confidence interval", "CI", "95% CI", "confidence limits"]
      patterns: ["confidence.*interval", "\\bCI\\b", "95%.*CI", "\\[.*,.*\\]"]
      minimum_pattern_matches: 2
    validation:
      confidence_threshold: 0.8

  - id: "RESULTS_004"
    name: "Safety Profile Reporting"
    description: "Comprehensive safety profile must be reported"
    category: "reporting"
    severity: "critical"
    type: "evidence_ratio"
    parameters:
      findings_terms: ["adverse event", "side effect", "safety", "tolerability"]
      evidence_terms: ["frequency", "incidence", "percentage", "rate", "number", "count"]
      minimum_ratio: 0.7
    validation:
      confidence_threshold: 0.7

  # ========================================
  # DRUG ACCOUNTABILITY REQUIREMENTS
  # ========================================

  - id: "DRUG_001"
    name: "Drug Accountability"
    description: "Drug accountability and dispensing procedures must be documented"
    category: "drug_management"
    severity: "critical"
    type: "content_pattern"
    parameters:
      search_terms: ["drug accountability", "medication dispensing", "study drug", "placebo", "investigational product"]
      required_patterns: ["drug.*accountability", "study.*drug", "investigational.*product", "placebo"]
      minimum_pattern_matches: 2
    validation:
      confidence_threshold: 0.8

  - id: "DRUG_002"
    name: "Concomitant Medications"
    description: "Concomitant medication policies must be specified"
    category: "drug_management"
    severity: "warning"
    type: "content_pattern"
    parameters:
      search_terms: ["concomitant medication", "concurrent therapy", "prohibited medication", "allowed medication"]
      patterns: ["concomitant.*medication", "concurrent.*therapy", "prohibited.*medication"]
      minimum_pattern_matches: 1
    validation:
      confidence_threshold: 0.6

  # ========================================
  # CUSTOM VALIDATION RULES
  # ========================================

  - id: "CUSTOM_001"
    name: "Phase II Specific Requirements"
    description: "Phase II trial must meet specific regulatory requirements"
    category: "phase_specific"
    severity: "critical"
    type: "custom_logic"
    parameters:
      search_terms: ["Phase II", "Phase 2", "efficacy", "safety", "dose-finding"]
      required_elements:
        - "efficacy_assessment"
        - "safety_evaluation"
        - "dose_confirmation"
        - "patient_population"
      logic_type: "majority_required"
    validation:
      confidence_threshold: 0.8
      custom_validator: "phase2_requirements_validator"

  - id: "CUSTOM_002"
    name: "Regulatory Submission Readiness"
    description: "Study must be ready for regulatory submission"
    category: "regulatory"
    severity: "critical"
    type: "custom_logic"
    parameters:
      search_terms: ["FDA", "regulatory", "submission", "IND", "clinical study report"]
      required_elements:
        - "protocol_compliance"
        - "data_integrity"
        - "safety_reporting"
        - "efficacy_results"
      logic_type: "all_required"
    validation:
      confidence_threshold: 0.9
      custom_validator: "regulatory_submission_validator"

# ========================================
# RULE CATEGORIES CONFIGURATION
# ========================================

categories:
  regulatory:
    description: "Regulatory compliance and approval requirements"
    default_severity: "critical"
  study_design:
    description: "Clinical study design requirements"
    default_severity: "critical"
  safety:
    description: "Patient safety monitoring requirements"
    default_severity: "critical"
  statistics:
    description: "Statistical analysis requirements"
    default_severity: "critical"
  data_quality:
    description: "Data quality and management requirements"
    default_severity: "critical"
  reporting:
    description: "Results reporting requirements"
    default_severity: "warning"
  drug_management:
    description: "Investigational product management"
    default_severity: "critical"
  phase_specific:
    description: "Phase II trial specific requirements"
    default_severity: "critical"

# ========================================
# SEARCH CONFIGURATION MAPPING
# ========================================

search_configs:
  default: "COMBINED_HYBRID_SEARCH_RRF"
  regulatory: "EDGE_HYBRID_SEARCH_CROSS_ENCODER"
  safety: "EDGE_HYBRID_SEARCH_RRF"
  statistics: "NODE_HYBRID_SEARCH_RRF"
  reporting: "COMBINED_HYBRID_SEARCH_RRF"

# ========================================
# GLOBAL SETTINGS
# ========================================

settings:
  default_confidence_threshold: 0.75
  max_evidence_items: 5
  enable_cross_validation: true
  parallel_rule_execution: true
  
  # Phase II specific settings
  phase2_requirements:
    minimum_safety_events: 10
    minimum_efficacy_endpoints: 1
    required_regulatory_approvals: ["IRB", "FDA_IND"]
    
  # Severity escalation rules
  severity_escalation:
    critical_failure_threshold: 1  # Any critical failure fails the whole study
    warning_failure_threshold: 5   # More than 5 warnings escalate to critical
    
  # Reporting thresholds
  reporting_thresholds:
    adverse_event_reporting: 0.9    # 90% confidence required
    efficacy_reporting: 0.8         # 80% confidence required
    regulatory_compliance: 0.95     # 95% confidence required