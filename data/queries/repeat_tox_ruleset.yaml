# Repeat Toxicology Study Report
# Compliance Rules Configuration
# Based on GLP Guidelines, FDA/EMA Guidance, and OECD Test Guidelines

rules:
  # ========================================
  # REGULATORY COMPLIANCE RULES
  # ========================================
  
  - id: "REG_001"
    name: "GLP Compliance Statement"
    description: "Study must document compliance with Good Laboratory Practice standards"
    category: "regulatory"
    severity: "critical"
    type: "content_pattern"
    parameters:
      search_terms: ["GLP", "good laboratory practice", "GLP compliance", "GLP standards"]
      required_patterns: ["GLP.*complian", "good.*laboratory.*practice", "GLP.*standard"]
      minimum_pattern_matches: 1
    validation:
      confidence_threshold: 0.9

  - id: "REG_002"
    name: "Regulatory Guideline Reference"
    description: "Study must reference applicable regulatory guidelines (FDA, EMA, OECD)"
    category: "regulatory"
    severity: "critical"
    type: "content_pattern"
    parameters:
      search_terms: ["FDA guidance", "EMA guideline", "OECD test guideline", "ICH guideline"]
      required_patterns: ["FDA.*guidance", "EMA.*guideline", "OECD.*test.*guideline", "ICH.*guideline"]
      minimum_pattern_matches: 1
    validation:
      confidence_threshold: 0.85

  - id: "REG_003"
    name: "Test Facility Registration"
    description: "Test facility must be documented as registered/accredited"
    category: "regulatory"
    severity: "critical"
    type: "content_pattern"
    parameters:
      search_terms: ["test facility", "laboratory registration", "GLP certification", "accreditation"]
      required_patterns: ["test.*facility.*registr", "laboratory.*registr", "GLP.*certif", "accreditat"]
      minimum_pattern_matches: 1
    validation:
      confidence_threshold: 0.8

  # ========================================
  # ANIMAL WELFARE REQUIREMENTS
  # ========================================

  - id: "WELFARE_001"
    name: "IACUC Approval Documentation"
    description: "Institutional Animal Care and Use Committee approval must be documented"
    category: "animal_welfare"
    severity: "critical"
    type: "content_pattern"
    parameters:
      search_terms: ["IACUC", "institutional animal care", "animal ethics", "animal welfare committee"]
      required_patterns: ["IACUC.*approv", "institutional.*animal.*care", "animal.*ethics.*approv"]
      minimum_pattern_matches: 1
    validation:
      confidence_threshold: 0.9

  - id: "WELFARE_002"
    name: "Animal Housing Conditions"
    description: "Animal housing and environmental conditions must be documented"
    category: "animal_welfare"
    severity: "warning"
    type: "content_pattern"
    parameters:
      search_terms: ["housing conditions", "environmental conditions", "temperature", "humidity", "light cycle"]
      required_patterns: ["housing.*condition", "environmental.*condition", "temperature.*humidity", "light.*cycle"]
      minimum_pattern_matches: 2
    validation:
      confidence_threshold: 0.75

  - id: "WELFARE_003"
    name: "Humane Endpoint Criteria"
    description: "Humane endpoint criteria and monitoring procedures must be defined"
    category: "animal_welfare"
    severity: "critical"
    type: "content_pattern"
    parameters:
      search_terms: ["humane endpoint", "euthanasia criteria", "moribund criteria", "welfare monitoring"]
      required_patterns: ["humane.*endpoint", "euthanasia.*criteria", "moribund.*criteria"]
      minimum_pattern_matches: 1
    validation:
      confidence_threshold: 0.85

  # ========================================
  # STUDY DESIGN REQUIREMENTS
  # ========================================

  - id: "DESIGN_001"
    name: "Dose Selection Rationale"
    description: "Rationale for dose selection and dose levels must be provided"
    category: "study_design"
    severity: "critical"
    type: "content_pattern"
    parameters:
      search_terms: ["dose selection", "dose rationale", "dose levels", "maximum tolerated dose", "MTD"]
      required_patterns: ["dose.*selection", "dose.*rationale", "dose.*level", "maximum.*tolerated.*dose", "\\bMTD\\b"]
      minimum_pattern_matches: 2
    validation:
      confidence_threshold: 0.8

  - id: "DESIGN_002"
    name: "Study Duration Justification"
    description: "Study duration and treatment period must be justified"
    category: "study_design"
    severity: "warning"
    type: "content_pattern"
    parameters:
      search_terms: ["study duration", "treatment period", "dosing duration", "exposure period"]
      required_patterns: ["study.*duration", "treatment.*period", "dosing.*duration", "exposure.*period"]
      minimum_pattern_matches: 1
    validation:
      confidence_threshold: 0.75

  - id: "DESIGN_003"
    name: "Route of Administration"
    description: "Route of administration must be clearly documented and justified"
    category: "study_design"
    severity: "critical"
    type: "content_pattern"
    parameters:
      search_terms: ["route of administration", "oral administration", "intravenous", "subcutaneous", "dermal"]
      required_patterns: ["route.*administration", "oral.*administration", "intravenous", "subcutaneous", "dermal"]
      minimum_pattern_matches: 1
    validation:
      confidence_threshold: 0.85

  # ========================================
  # PATHOLOGY REQUIREMENTS
  # ========================================

  - id: "PATH_001"
    name: "Histopathology Procedures"
    description: "Histopathology procedures and organ examination must be documented"
    category: "pathology"
    severity: "critical"
    type: "content_pattern"
    parameters:
      search_terms: ["histopathology", "microscopic examination", "organ examination", "tissue processing"]
      required_patterns: ["histopatholog", "microscopic.*examination", "organ.*examination", "tissue.*processing"]
      minimum_pattern_matches: 2
    validation:
      confidence_threshold: 0.8

  - id: "PATH_002"
    name: "Necropsy Procedures"
    description: "Necropsy procedures and gross pathology findings must be documented"
    category: "pathology"
    severity: "critical"
    type: "content_pattern"
    parameters:
      search_terms: ["necropsy", "gross pathology", "macroscopic examination", "post-mortem examination"]
      required_patterns: ["necropsy", "gross.*pathology", "macroscopic.*examination", "post.*mortem.*examination"]
      minimum_pattern_matches: 1
    validation:
      confidence_threshold: 0.85

  - id: "PATH_003"
    name: "Pathologist Qualifications"
    description: "Pathologist qualifications and experience must be documented"
    category: "pathology"
    severity: "warning"
    type: "content_pattern"
    parameters:
      search_terms: ["pathologist", "board certified", "veterinary pathologist", "pathology experience"]
      required_patterns: ["pathologist.*qualif", "board.*certified", "veterinary.*pathologist"]
      minimum_pattern_matches: 1
    validation:
      confidence_threshold: 0.75

  # ========================================
  # SAFETY MONITORING REQUIREMENTS
  # ========================================

  - id: "SAFETY_001"
    name: "Clinical Observations Protocol"
    description: "Clinical observations and monitoring procedures must be documented"
    category: "safety"
    severity: "critical"
    type: "content_pattern"
    parameters:
      search_terms: ["clinical observations", "daily observations", "clinical signs", "behavioral assessment"]
      required_patterns: ["clinical.*observation", "daily.*observation", "clinical.*sign", "behavioral.*assessment"]
      minimum_pattern_matches: 2
    validation:
      confidence_threshold: 0.8

  - id: "SAFETY_002"
    name: "Mortality and Morbidity Recording"
    description: "Mortality and morbidity data recording procedures must be documented"
    category: "safety"
    severity: "critical"
    type: "content_pattern"
    parameters:
      search_terms: ["mortality", "morbidity", "deaths", "moribund animals", "survival"]
      required_patterns: ["mortality", "morbidity", "death.*record", "moribund.*animal", "survival.*data"]
      minimum_pattern_matches: 1
    validation:
      confidence_threshold: 0.85

  - id: "SAFETY_003"
    name: "Body Weight Monitoring"
    description: "Body weight monitoring procedures and frequency must be documented"
    category: "safety"
    severity: "warning"
    type: "content_pattern"
    parameters:
      search_terms: ["body weight", "weight monitoring", "weekly weighing", "weight change"]
      required_patterns: ["body.*weight", "weight.*monitor", "weekly.*weigh", "weight.*change"]
      minimum_pattern_matches: 1
    validation:
      confidence_threshold: 0.75

  # ========================================
  # DATA QUALITY REQUIREMENTS
  # ========================================

  - id: "DATA_001"
    name: "Statistical Analysis Plan"
    description: "Statistical analysis methods and procedures must be documented"
    category: "data_quality"
    severity: "critical"
    type: "content_pattern"
    parameters:
      search_terms: ["statistical analysis", "statistical methods", "data analysis", "statistical test"]
      required_patterns: ["statistical.*analysis", "statistical.*method", "data.*analysis", "statistical.*test"]
      minimum_pattern_matches: 1
    validation:
      confidence_threshold: 0.8

  - id: "DATA_002"
    name: "Data Integrity Procedures"
    description: "Data integrity and quality assurance procedures must be documented"
    category: "data_quality"
    severity: "critical"
    type: "content_pattern"
    parameters:
      search_terms: ["data integrity", "quality assurance", "data verification", "audit trail"]
      required_patterns: ["data.*integrity", "quality.*assurance", "data.*verification", "audit.*trail"]
      minimum_pattern_matches: 1
    validation:
      confidence_threshold: 0.85

  - id: "DATA_003"
    name: "Raw Data Documentation"
    description: "Raw data handling and documentation procedures must be specified"
    category: "data_quality"
    severity: "warning"
    type: "content_pattern"
    parameters:
      search_terms: ["raw data", "source data", "data collection", "data recording"]
      required_patterns: ["raw.*data", "source.*data", "data.*collection", "data.*recording"]
      minimum_pattern_matches: 1
    validation:
      confidence_threshold: 0.75

  # ========================================
  # TOXICOKINETIC REQUIREMENTS
  # ========================================

  - id: "TK_001"
    name: "Toxicokinetic Sampling"
    description: "Toxicokinetic sampling procedures and timepoints must be documented"
    category: "toxicokinetics"
    severity: "warning"
    type: "content_pattern"
    parameters:
      search_terms: ["toxicokinetic", "TK sampling", "blood sampling", "plasma concentration", "pharmacokinetic"]
      required_patterns: ["toxicokinetic", "TK.*sampl", "blood.*sampl", "plasma.*concentration", "pharmacokinetic"]
      minimum_pattern_matches: 1
    validation:
      confidence_threshold: 0.8

  - id: "TK_002"
    name: "Bioanalytical Method Validation"
    description: "Bioanalytical method validation for test article quantification must be documented"
    category: "toxicokinetics"
    severity: "warning"
    type: "content_pattern"
    parameters:
      search_terms: ["bioanalytical method", "method validation", "analytical validation", "assay validation"]
      required_patterns: ["bioanalytical.*method", "method.*validation", "analytical.*validation", "assay.*validation"]
      minimum_pattern_matches: 1
    validation:
      confidence_threshold: 0.75