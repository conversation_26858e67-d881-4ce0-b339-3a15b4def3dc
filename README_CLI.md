# ARIA CLI v2.0

Modern, user-friendly command-line interface for <PERSON> (Automated Research Intelligence Assistant).

## Installation

```bash
pip install -r requirements.txt
```

### Create a Shell Alias for aria
If you want to use aria as a command, add this to your ~/.zshrc or ~/.bashrc:

```bash
alias aria='poetry run python -m aria_cli'
```

Then you can use:

```bash
aria query "What is the compound?" --project test_project
```

After adding the alias, run `source ~/.zshrc` (or `source ~/.bashrc`) to activate it.


## Quick Start

```bash
# Check system health
aria doctor check

# Set up configuration
aria config init

# Add a project
aria project add my_study ./data/raw_documents

# Run a query
aria query "What were the primary endpoints?" --project my_study

# Check compliance rules
aria rules check --group my_study_group --config rules.yaml
```

## Commands

### Project Management

- `aria project add <n> <path>` - Add and process new project
- `aria project add-preprocessed <n> <path>` - Add preprocessed project
- `aria project list` - List all projects
- `aria project info <n>` - Show project details
- `aria project remove <n>` - Remove project

### Querying

- `aria query "question" --project <n>` - Run natural language query
- `aria query q "question" -p <n>` - Quick query alias

### Rule Checking

- `aria rules check --group <id>` - Check rules against group
- `aria rules validate-config <file>` - Validate rules configuration

### Configuration

- `aria config show` - Show current configuration
- `aria config set <key> <value>` - Set configuration value
- `aria config init` - Interactive setup
- `aria config test` - Test configuration

### System

- `aria doctor check` - Run health diagnostics
- `aria completion <shell>` - Generate shell completion

### Global Options

- `--verbose, -v` - Enable verbose logging
- `--quiet, -q` - Suppress non-essential output
- `--config <file>` - Use custom config file
- `--version` - Show version

## Configuration
Configuration is loaded from:

- Environment variables (`ARIA_*`)
- `.env` file in current directory
- Command-line config file
- Built-in defaults

### Environment Variables

- `ARIA_NEO4J_URI` - Neo4j database URI
- `ARIA_NEO4J_USER` - Neo4j username
- `ARIA_NEO4J_PASSWORD` - Neo4j password
- `ARIA_PROJECTS_FILE` - Projects registry file
- `ARIA_DEFAULT_RULES_CONFIG` - Default rules config file
- `ARIA_LOG_LEVEL` - Logging level

## Exit Codes

- `0` - Success
- `1` - Usage/argument errors
- `2` - Processing failures
- `3` - External dependency errors (database, filesystem)
- `99` - Unexpected errors
- `130` - Interrupted by user (Ctrl+C)
