import asyncio
import datetime
import json
import yaml
from aria.memory.graphiti_runtime_manager import GraphitiRuntimeManager
from aria.db_service import get_graphiti_client

# Replace with your actual rule YAML path and group_id/project context
RULE_YAML_PATH = "data/queries/repeat_tox_ruleset.yaml"
RULE_ID = "REG_001"
GROUP_ID = "project_demo"

async def main():
    # Load rule config
    with open(RULE_YAML_PATH) as f:
        ruleset = yaml.safe_load(f)["rules"]
    rule = next(r for r in ruleset if r["id"] == RULE_ID)
    print(rule)
    search_terms = rule["parameters"]["search_terms"]

    # Initialize GraphitiManager with the required Graphiti client
    graphiti_client = get_graphiti_client()
    graphiti_runtime_manager = GraphitiRuntimeManager(graphiti_client)
    # Run search
    print(f"Searching for: {search_terms} in group {GROUP_ID}")
    results = await graphiti_runtime_manager.search(" ".join(search_terms), group_ids=[GROUP_ID])

    with open("results.json", "w") as f:
        json.dump(results.model_dump(), f, default=lambda o: o.isoformat() if isinstance(o, datetime.datetime) else str(o))
        
    # Try common attributes:
    if hasattr(results, "results"):
        results_list = results.results
    elif hasattr(results, "items"):
        results_list = results.items
    elif hasattr(results, "data"):
        results_list = results.data
    else:
        # fallback: maybe it's iterable?
        try:
            results_list = list(results)
        except Exception:
            raise Exception(f"Unknown SearchResults type: {type(results)}")
    
    print(f"\nTotal results: {len(results_list)}")
    for i, res in enumerate(results_list):
        print(f"\nResult {i+1}:")
        if isinstance(res, tuple):
            for idx, item in enumerate(res):
                print(f"  [{idx}]: {item}")
        elif hasattr(res, "__dict__"):
            for k, v in res.__dict__.items():
                print(f"  {k}: {v}")
        else:
            print(f"  {res}")
    
    print("\n--- Summary ---")
    # Try to print first 5 facts if possible
    facts = []
    for r in results_list[:5]:
        if isinstance(r, tuple) and hasattr(r[0], "fact"):
            facts.append(r[0].fact)
        elif hasattr(r, "fact"):
            facts.append(r.fact)
        else:
            facts.append(str(r))
    print(f"First 5 facts: {facts}")

if __name__ == "__main__":
    asyncio.run(main())