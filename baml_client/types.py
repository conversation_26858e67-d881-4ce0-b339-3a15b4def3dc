###############################################################################
#
#  Welcome to Baml! To use this generated code, please run the following:
#
#  $ pip install baml-py
#
###############################################################################

# This file was generated by BAML: please do not edit it. Instead, edit the
# BAML files and re-generate this code.
#
# ruff: noqa: E501,F401
# flake8: noqa: E501,F401
# pylint: disable=unused-import,line-too-long
# fmt: off
import baml_py
from enum import Enum
from pydantic import BaseModel, ConfigDict
from typing_extensions import TypeAlias
from typing import Dict, Generic, List, Literal, Optional, TypeVar, Union


T = TypeVar('T')
CheckName = TypeVar('CheckName', bound=str)

class Check(BaseModel):
    name: str
    expression: str
    status: str

class Checked(BaseModel, Generic[T,CheckName]):
    value: T
    checks: Dict[CheckName, Check]

def get_checks(checks: Dict[CheckName, Check]) -> List[Check]:
    return list(checks.values())

def all_succeeded(checks: Dict[CheckName, Check]) -> bool:
    return all(check.status == "succeeded" for check in get_checks(checks))



class DocumentSummary(BaseModel):
    title: Optional[str] = None
    summary: Optional[str] = None
    keywords: Optional[List[str]] = None

class Headers(BaseModel):
    line: int
    title: str
    parent: Optional[int] = None

class QueryClassification(BaseModel):
    category: str
    confidence: float
    reasoning: List[str]
    entities: List["QueryEntity"]
    search_strategy: "SearchStrategy"
    query_intent: str
    original_query: str

class QueryEntity(BaseModel):
    name: str
    type: str
    attributes: List[str]

class Resume(BaseModel):
    name: str
    email: str
    experience: List[str]
    skills: List[str]

class SearchStrategy(BaseModel):
    primary_target: str
    approach: str
    reranking: Optional[str] = None
    explanation: str

class TocHierarchy(BaseModel):
    sections: List["TocSection"]

class TocSection(BaseModel):
    title: str
    number: Optional[str] = None
    referred_section: Optional[str] = None
    content: Optional[str] = None
    children: List["TocSection"]
