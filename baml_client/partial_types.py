###############################################################################
#
#  Welcome to Baml! To use this generated code, please run the following:
#
#  $ pip install baml-py
#
###############################################################################

# This file was generated by BAML: please do not edit it. Instead, edit the
# BAML files and re-generate this code.
#
# ruff: noqa: E501,F401
# flake8: noqa: E501,F401
# pylint: disable=unused-import,line-too-long
# fmt: off
import baml_py
from enum import Enum
from pydantic import BaseModel, ConfigDict
from typing_extensions import TypeAlias
from typing import Dict, Generic, List, Optional, TypeVar, Union, Literal

from . import types
from .types import Checked, Check

###############################################################################
#
#  These types are used for streaming, for when an instance of a type
#  is still being built up and any of its fields is not yet fully available.
#
###############################################################################

T = TypeVar('T')
class StreamState(BaseModel, Generic[T]):
    value: T
    state: Literal["Pending", "Incomplete", "Complete"]


class DocumentSummary(BaseModel):
    title: Optional[str] = None
    summary: Optional[str] = None
    keywords: Optional[List[str]] = None

class Headers(BaseModel):
    line: Optional[int] = None
    title: Optional[str] = None
    parent: Optional[int] = None

class QueryClassification(BaseModel):
    category: Optional[str] = None
    confidence: Optional[float] = None
    reasoning: List[str]
    entities: List["QueryEntity"]
    search_strategy: Optional["SearchStrategy"] = None
    query_intent: Optional[str] = None
    original_query: Optional[str] = None

class QueryEntity(BaseModel):
    name: Optional[str] = None
    type: Optional[str] = None
    attributes: List[str]

class Resume(BaseModel):
    name: Optional[str] = None
    email: Optional[str] = None
    experience: List[str]
    skills: List[str]

class SearchStrategy(BaseModel):
    primary_target: Optional[str] = None
    approach: Optional[str] = None
    reranking: Optional[str] = None
    explanation: Optional[str] = None

class TocHierarchy(BaseModel):
    sections: List["TocSection"]

class TocSection(BaseModel):
    title: Optional[str] = None
    number: Optional[str] = None
    referred_section: Optional[str] = None
    content: Optional[str] = None
    children: List["TocSection"]
