###############################################################################
#
#  Welcome to Baml! To use this generated code, please run the following:
#
#  $ pip install baml-py
#
###############################################################################

# This file was generated by BAML: please do not edit it. Instead, edit the
# BAML files and re-generate this code.
#
# ruff: noqa: E501,F401
# flake8: noqa: E501,F401
# pylint: disable=unused-import,line-too-long
# fmt: off

file_map = {
    
    "clients.baml": "// Learn more about clients at https://docs.boundaryml.com/docs/snippets/clients/overview\nclient<llm> Smart {\n  provider openai\n  options {\n    model \"gpt-4.1\"\n    api_key env.OPENAI_API_KEY\n  }\n}\n\nclient<llm> Efficient {\n  provider openai\n  options {\n    model \"gpt-4.1-mini\"\n    api_key env.OPENAI_API_KEY\n  }\n}\n\nclient<llm> Fast {\n  provider openai\n  options {\n    model \"gpt-4.1-nano\"\n    api_key env.OPENAI_API_KEY\n  }\n}\n\nclient<llm> CustomGPT4o {\n  provider openai\n  options {\n    model \"gpt-4o\"\n    api_key env.OPENAI_API_KEY\n  }\n}\n\nclient<llm> CustomGPT4oMini {\n  provider openai\n  retry_policy Exponential\n  options {\n    model \"gpt-4o-mini\"\n    api_key env.OPENAI_API_KEY\n  }\n}\n\nclient<llm> CustomSonnet {\n  provider anthropic\n  options {\n    model \"claude-3-5-sonnet-20241022\"\n    api_key env.ANTHROPIC_API_KEY\n  }\n}\n\n\nclient<llm> CustomHaiku {\n  provider anthropic\n  retry_policy Constant\n  options {\n    model \"claude-3-haiku-20240307\"\n    api_key env.ANTHROPIC_API_KEY\n  }\n}\n\n// https://docs.boundaryml.com/docs/snippets/clients/round-robin\nclient<llm> CustomFast {\n  provider round-robin\n  options {\n    // This will alternate between the two clients\n    strategy [CustomGPT4oMini, CustomHaiku]\n  }\n}\n\n// https://docs.boundaryml.com/docs/snippets/clients/fallback\nclient<llm> OpenaiFallback {\n  provider fallback\n  options {\n    // This will try the clients in order until one succeeds\n    strategy [CustomGPT4oMini, CustomGPT4oMini]\n  }\n}\n\n// https://docs.boundaryml.com/docs/snippets/clients/retry\nretry_policy Constant {\n  max_retries 3\n  // Strategy is optional\n  strategy {\n    type constant_delay\n    delay_ms 200\n  }\n}\n\nretry_policy Exponential {\n  max_retries 2\n  // Strategy is optional\n  strategy {\n    type exponential_backoff\n    delay_ms 300\n    multiplier 1.5\n    max_delay_ms 10000\n  }\n}",
    "document_summarizer.baml": "class DocumentSummary {\n    title string? @description(\"document title\")\n    summary string? @description(\"single sentence summary of the document\")\n    keywords string[]? @description(\"keywords extracted from the document to be able to search\")\n}\n\nfunction DocumentSummarizer(markdown: string) -> DocumentSummary {\n    client Fast\n    prompt #\"\n        _.role(system)\nYou are **ARIA-Summarizer v2.0**, an expert scientific document summarizer.\nYour job is to transform a document or a section from a document into a strict JSON object that conforms **exactly** to the following schema (no additional keys, no comments):\n\n{{ ctx.output_format }}\n\nStrict formatting rules:\n1. **Return only the JSON.** Do NOT wrap in triple back-ticks or add commentary.\n2. If a field is unknown or not applicable, set it to null (not omitted).\n\n_.role(user)\n\nHere is the markdown:\n{{ markdown }}\n\n    \"#\n}\n\ntest TestDocumentSummarizer {\n    functions [DocumentSummarizer]\n    args {\n        markdown #\"\n### 1.2 Investigational Product\nXDR-112 is an orally administered JAK1 inhibitor with a half-life of approximately 12 hours, allowing for once-daily dosing. Phase I studies in healthy\\n\\nvolunteers demonstrated favorable pharmacokinetics and an acceptable safety profile at doses up to 30 mg daily.\n\"#\n    }\n    @@assert({{this.title == \"1.2 Investigational Product\"}})\n}\n    ",
    "generators.baml": "// This helps use auto generate libraries you can use in the language of\n// your choice. You can have multiple generators if you use multiple languages.\n// Just ensure that the output_dir is different for each generator.\ngenerator target {\n    // Valid values: \"python/pydantic\", \"typescript\", \"ruby/sorbet\", \"rest/openapi\"\n    output_type \"python/pydantic\"\n\n    // Where the generated code will be saved (relative to baml_src/)\n    output_dir \"../\"\n\n    // The version of the BAML package you have installed (e.g. same version as your baml-py or @boundaryml/baml).\n    // The BAML VSCode extension version should also match this version.\n    version \"0.88.0\"\n\n    // Valid values: \"sync\", \"async\"\n    // This controls what `b.FunctionName()` will be (sync or async).\n    default_client_mode sync\n}\n",
    "query_parser.baml": "// Updated BAML classes for query classification\n\nclass QueryEntity {\n    name        string     @description(\"entity name, e.g. \\\"drug\\\", \\\"patient\\\", \\\"study\\\"\")\n    type        string     @description(\"entity type, e.g. \\\"drug\\\", \\\"person\\\", \\\"measurement\\\", \\\"condition\\\"\")\n    attributes  string[]   @description(\"relevant attributes or qualifiers, e.g. [\\\"male\\\", \\\"rat\\\"] for \\\"male rats\\\"\")\n}\n\nclass SearchStrategy {\n    primary_target   string    @description(\"what to search for: \\\"nodes\\\" | \\\"edges\\\" | \\\"communities\\\" | \\\"paths\\\" | \\\"aggregation\\\"\")\n    approach        string    @description(\"search approach: \\\"semantic\\\" | \\\"graph_distance\\\" | \\\"temporal\\\" | \\\"comparative\\\" | \\\"comprehensive\\\"\")\n    reranking       string?   @description(\"reranking method: \\\"rrf\\\" | \\\"mmr\\\" | \\\"cross_encoder\\\" | \\\"node_distance\\\" | \\\"episode_mentions\\\" | null\")\n    explanation     string    @description(\"brief explanation of why this strategy fits the query\")\n}\n\nclass QueryClassification {\n    category        string        @description(\"one of: \\\"entity_identification\\\" | \\\"fact_relationship\\\" | \\\"contextual_proximity\\\" | \\\"path_multihop\\\" | \\\"aggregation_meta\\\" | \\\"temporal\\\" | \\\"comparative\\\" | \\\"exploratory\\\" | \\\"verification\\\"\")\n    confidence      float         @description(\"confidence score between 0.0 and 1.0\")\n    reasoning       string[]      @description(\"list of specific reasons for this classification\")\n    entities        QueryEntity[] @description(\"key entities identified in the query\")\n    search_strategy SearchStrategy @description(\"recommended search strategy for this query type\")\n    query_intent    string        @description(\"natural language description of what the user is trying to find\")\n    original_query  string        @description(\"verbatim copy of user query\")\n}\n\nfunction QueryClassifier(query: string) -> QueryClassification {\n    client Fast\n    prompt #\"\n        _.role(system)\nYou are **ARIA-Classify v2.0**, an expert query classification system for knowledge graph search.\n\nYour job is to analyze natural language queries and classify them into one of 9 categories, each optimized for different search strategies:\n\n**CATEGORIES & SEARCH STRATEGIES:**\n\n1. **entity_identification** - \"What is X?\" \"Who is Y?\" \"Find the drug\"\n   - Target: nodes (entities)\n   - Approach: semantic search for entity names and descriptions\n   - Best for: Finding specific people, drugs, compounds, patients, researchers\n\n2. **fact_relationship** - \"X causes Y\" \"How does Z work?\" \"What effects does A have?\"\n   - Target: edges (facts/relationships) \n   - Approach: semantic search for relationship statements\n   - Best for: Finding specific claims, causal relationships, mechanisms\n\n3. **contextual_proximity** - \"Everything about X\" \"All information on Y\" \"Tell me about Z\"\n   - Target: edges reranked by graph distance\n   - Approach: find central entity, then rank facts by proximity\n   - Best for: Comprehensive information around a specific entity\n\n4. **path_multihop** - \"How is X connected to Y?\" \"Path from A to B\" \"Chain of events\"\n   - Target: paths between entities\n   - Approach: graph traversal and path finding\n   - Best for: Multi-hop relationships, causal chains\n\n5. **aggregation_meta** - \"How many X?\" \"List all Y\" \"Count of Z\" \"Total number\"\n   - Target: aggregation of nodes/communities\n   - Approach: comprehensive search with counting/grouping\n   - Best for: Quantitative queries, lists, counts, statistics\n\n6. **temporal** - \"When did X happen?\" \"Before/after Y\" \"Timeline of Z\"\n   - Target: edges with temporal filtering\n   - Approach: temporal search with episode context\n   - Best for: Time-based queries, chronology, sequences\n\n7. **comparative** - \"X vs Y\" \"Compare A and B\" \"Difference between X and Y\"\n   - Target: edges with diversity ranking\n   - Approach: parallel search with diverse results\n   - Best for: Comparisons, contrasts, similarities/differences\n\n8. **exploratory** - \"What's interesting?\" \"Show insights\" \"Discover patterns\"\n   - Target: comprehensive search across all types\n   - Approach: broad search for discovery\n   - Best for: Open-ended exploration, pattern discovery\n\n9. **verification** - \"Is X true?\" \"Does Y cause Z?\" \"Evidence for X\"\n   - Target: edges with confidence scoring\n   - Approach: cross-encoder for relevance scoring\n   - Best for: Truth verification, evidence gathering, confirmation\n\n**OUTPUT FORMAT:**\nReturn a JSON object that conforms **exactly** to the QueryClassification schema. No additional keys, no comments, no markdown formatting.\n\n**CLASSIFICATION RULES:**\n- Choose the MOST SPECIFIC category that fits\n- Confidence should reflect how clearly the query fits the category (0.0-1.0)\n- Provide 2-4 specific reasoning points\n- Identify key entities with their types and attributes\n- Recommend the optimal search strategy\n- If uncertain between categories, prefer the more specific one\n\n**OUTPUT FORMAT:**\n{{ ctx.output_format }}\n        _.role(user)\n\nClassify this query:\n{{ query }}\n\n    \"#\n}\n\n// Alternative function for batch classification\nfunction BatchQueryClassifier(queries: string[]) -> QueryClassification[] {\n    client Fast\n    prompt #\"\n        _.role(system)\nYou are **ARIA-Classify v2.0**, classifying multiple queries at once.\n\nUse the same 9-category system and search strategies as the single query classifier.\n\nReturn a JSON array of QueryClassification objects, one for each input query in the same order.\n\nOutput format:\n\n{{ ctx.output_format }}\n\n        _.role(user)\n\nClassify these queries:\n{% for query in queries %}\n{{ loop.index }}. {{ query }}\n{% endfor %}\n\n    \"#\n}\n\n// Function to explain classification decisions in detail\nfunction ExplainQueryClassification(query: string, category: string) -> string {\n    client Fast\n    prompt #\"\n        _.role(system)\nYou are **ARIA-Explain v2.0**, providing detailed explanations for query classifications.\n\nGiven a query and its assigned category, explain in detail:\n1. Why this category was chosen\n2. What linguistic patterns led to this decision\n3. What search strategy would be optimal and why\n4. Any alternative categories that were considered\n\nBe educational and thorough in your explanation.\n\n{{ ctx.output_format }}\n        _.role(user)\n\nQuery: {{ query }}\nAssigned Category: {{ category }}\n\nProvide a detailed explanation of this classification decision.\n\n    \"#\n}\n\n// Test cases for the classifier\ntest TestEntityIdentification {\n    functions [QueryClassifier]\n    args {\n        query \"What is the drug of interest?\"\n    }\n}\n\ntest TestFactRelationship {\n    functions [QueryClassifier]\n    args {\n        query \"What side effects does the drug cause?\"\n    }\n}\n\ntest TestContextualProximity {\n    functions [QueryClassifier]\n    args {\n        query \"Tell me everything about the liver toxicity study\"\n    }\n}\n\ntest TestAggregationMeta {\n    functions [QueryClassifier]\n    args {\n        query \"How many patients experienced adverse events?\"\n    }\n}\n\ntest TestComparative {\n    functions [QueryClassifier]\n    args {\n        query \"Compare the side effects in male vs female rats\"\n    }\n}\n\ntest TestTemporal {\n    functions [QueryClassifier]\n    args {\n        query \"When were the first symptoms observed?\"\n    }\n}\n\ntest TestVerification {\n    functions [QueryClassifier]\n    args {\n        query \"Is there evidence that the drug causes liver damage?\"\n    }\n}\n\ntest TestExploratory {\n    functions [QueryClassifier]\n    args {\n        query \"What interesting patterns emerge from the study data?\"\n    }\n}\n\ntest TestBatchClassification {\n    functions [BatchQueryClassifier]\n    args {\n        queries [\n            \"What is the primary endpoint?\",\n            \"How many subjects completed the study?\",\n            \"Compare treatment vs control groups\"\n        ]\n    }\n}\n\ntest TestExplainQueryClassification {\n    functions [ExplainQueryClassification]\n    args {\n        query \"What is the drug of interest?\"\n        category \"entity_identification\"\n    }\n}",
    "resume.baml": "// Defining a data model.\nclass Resume {\n  name string\n  email string\n  experience string[]\n  skills string[]\n}\n\n// Create a function to extract the resume from a string.\nfunction ExtractResume(resume: string) -> Resume {\n  // Specify a client as provider/model-name\n  // you can use custom LLM params with a custom client name from clients.baml like \"client CustomHaiku\"\n  client \"openai/gpt-4o\" // Set OPENAI_API_KEY to use this client.\n  prompt #\"\n    Extract from this content:\n    {{ resume }}\n\n    {{ ctx.output_format }}\n  \"#\n}\n\n\n\n// Test the function with a sample resume. Open the VSCode playground to run this.\ntest vaibhav_resume {\n  functions [ExtractResume]\n  args {\n    resume #\"\n      Vaibhav Gupta\n      <EMAIL>\n\n      Experience:\n      - Founder at BoundaryML\n      - CV Engineer at Google\n      - CV Engineer at Microsoft\n\n      Skills:\n      - Rust\n      - C++\n    \"#\n  }\n}\n",
    "toc.baml": "class Headers {\n    line int @description(\"The line number of the header.\")\n    title string @description(\"The title of the header.\")\n    parent int? @description(\"The parent line number of the header.\")\n}\n\nclass TocSection {\n    // The title of the section.\n    title string @description(\"The title of the section.\")\n    // The numerical identifier of the section.\n    number string? @description(\"The numerical identifier of the section.\")\n    // Section number that the content is referring to, if available. If not available, return an empty string.\n    referred_section string? @description(\"Section number that the content is referring to, if available. If not available, return an empty string.\")\n    content string? @description(\"Enriched content from markdown\")\n    // An optional array containing nested subsections.\n    children TocSection[] @description(\"An optional array containing nested subsections.\")\n}\n\n\nclass TocHierarchy {\n    sections TocSection[] @description(\"The sections of the ToC.\")\n}\n\n\nfunction CleanMarkdown(chunk: string) -> Headers[] {\n    client Fast\n    prompt #\"\n    _.role(system)\n    \n   You are analyzing a document chunk with line references. The document is extracted from pdf with ocr and may not be nicely formatted, especially the hierarchical headers (or titles/subtitles) may not be consistent. \n   Identify all lines that appear to be headers (titles, subtitles, etc.) based on semantic and structural clues.\n\n   For each header line:\n    1. Note its line number\n    2. Write the title of the header\n    3. Identify its parent header by checking the structural clues, like 1., 1.1, 1.1.1, etc.\n    \n    Return your analysis in this format:\n    {{ ctx.output_format }}\n    \n    \n    Note:\n    - The line numbers are based on the original chunk.\n    - The parent line number is the line number of the parent header.\n    - All headers (or titles, subtitles, etc.) must have text.\n    - If there is no parent header, the parent line number is \"None\".\n    - Make sure the headers are consistent in terms of style, length, format, etc.\n    \n    _.role(user)\n    \n    Here's the chunk to analyze:\n    ```\n    {{chunk}}    \n    ```\n    \"#\n}\n\n\ntest TestCleanMarkdown {\n    functions [CleanMarkdown]\n    args {\n        chunk #\"\n[LINE-1]## TABLE OF CONTENTS\n[LINE-2]\n[LINE-3]1. INTRODUCTION.................. 4\n[LINE-4]2. STUDY OBJECTIVES............... 8\n[LINE-5]3. STUDY DESIGN.................. 12\n[LINE-6]4. SUBJECT POPULATION............. 18\n[LINE-7]5. TREATMENTS................... 25\n[LINE-8]6. STUDY PROCEDURES............... 32\n[LINE-9]7. SAFETY ASSESSMENTS............ 48\n[LINE-10]8. EFFICACY ASSESSMENTS........... 62\n[LINE-11]9. DATA MANAGEMENT............... 75\n[LINE-12]10. STATISTICAL CONSIDERATIONS..... 82\n[LINE-13]11. ETHICAL CONSIDERATIONS........ 98\n[LINE-14]12. APPENDICES.................. 105\n[LINE-15]\n[LINE-16]## 1. INTRODUCTION\n[LINE-17]\n[LINE-18]### 1.1 Background\n[LINE-19]\n[LINE-20]Rheumatoid arthritis (RA) is a chronic inflammatory disease affecting approximately $1 \\%$ of the global population. Current therapies include conventional DMARDs and biologic agents, but up to $40 \\%$ of patients show inadequate response to these treatments.\n[LINE-21]\n[LINE-22]XDR-112 is a novel small molecule JAK1 inhibitor that has demonstrated promising results in preclinical studies and Phase I trials. The compound selectively inhibits JAK1 signaling pathways involved in the inflammatory cascade in RA.\n[LINE-23]\n[LINE-24]### 1.2 Investigational Product\n[LINE-25]\n[LINE-26]XDR-112 is an orally administered JAK1 inhibitor with a half-life of approximately 12 hours, allowing for once-daily dosing. Phase I studies in healthy\nvolunteers demonstrated favorable pharmacokinetics and an acceptable safety profile at doses up to 30 mg daily.\n[LINE-27]\n[LINE-28]### 1.3 Rationale for Current Study \n[LINE-29]\n[LINE-30]This Phase II study aims to evaluate the efficacy and safety of two dose levels of XDR-112 compared to placebo in patients with moderate to severe RA who have had an inadequate response to methotrexate therapy. The study will inform dose selection and endpoint refinement for future Phase III trials.\n[LINE-31]\n\"#\n    }\n}\n\n\nfunction ExtractToc(markdown: string) -> TocHierarchy {\n    client Fast\n    prompt #\"\n    _.role(system)\n    \nIdentify the Table of Contents (ToC) section from a given markdown document, analyze its hierarchy, and return this hierarchy as a JSON array representing sections and subsections.\n\n- Focus on the structure and hierarchy of the ToC.\n- Ignore any page numbers present.\n- For each hierarchical node, include:\n  - 'title': The title of the section.\n  - 'number': The numerical identifier of the section.\n  - 'referred_section': Section number that the content is referring to, if available. If not available, return an empty string.\n  - 'children': An optional array containing nested subsections.\n  \n# Output Format\n\n{{ ctx.output_format }}\n\n# Example\n\nInput: A markdown document (longer and with more sections than the example):\n```\n# Table of Contents\n1. Introduction\n2. Methodology\n   2.1. Data Collection 3.2.1\n   2.2. Analysis 5.1.8\n3. Results\n   3.1. Findings\n   3.2. Discussion\n4. Conclusion\n```\n\nOutput:\n\n# Notes\n\n- Ensure the JSON array represents the explicit structure of the ToC sections and subsections.\n- The hierarchy should be correctly nested within the 'children' arrays where necessary.\n- Make sure you capture the referred section numbers if they exist. They could be document ids or section numbers in other documents.\n- Treat Table of Contents (ToC) as a special single section. \n    _.role(user)\n    \nHere is the markdown content:\n```\n{{ markdown }}\n```\n\nExtract the ToC hierarchy as described.\n    \"#\n}\n\n\ntest TestExtractToc {\n    functions [ExtractToc]\n    args {\n        markdown #\"\n# Table of Contents\n1. Introduction\n2. Methodology\n   2.1. Data Collection 3.2.1\n   2.2. Analysis 5.1.8\n3. Results\n   3.1. Findings\n   3.2. Discussion\n4. Conclusion\n\"#\n    }\n}\n    ",
}

def get_baml_files():
    return file_map